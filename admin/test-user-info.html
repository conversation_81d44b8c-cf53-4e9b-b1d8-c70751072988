<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户信息显示和登出功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-title {
            color: #333;
            border-bottom: 2px solid #667eea;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .header-demo {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 20px;
            background: #fff;
            border-bottom: 1px solid #e4e7ed;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            height: 60px;
            margin: 20px 0;
        }
        .header-left {
            flex: 1;
        }
        .header-right {
            display: flex;
            align-items: center;
        }
        .user-profile {
            display: flex;
            align-items: center;
            gap: 8px;
            padding: 8px 12px;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            background: rgba(102, 126, 234, 0.05);
            border: 1px solid rgba(102, 126, 234, 0.1);
        }
        .user-profile:hover {
            background: rgba(102, 126, 234, 0.1);
            border-color: rgba(102, 126, 234, 0.2);
            transform: translateY(-1px);
        }
        .user-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
        }
        .user-name {
            font-size: 14px;
            font-weight: 500;
            color: #1f2937;
            margin: 0 4px;
        }
        .dropdown-icon {
            font-size: 12px;
            color: #6b7280;
            transition: transform 0.3s ease;
        }
        .user-profile:hover .dropdown-icon {
            transform: rotate(180deg);
        }
        .feature-list {
            list-style-type: none;
            padding: 0;
        }
        .feature-list li {
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }
        .feature-list li:before {
            content: "✓ ";
            color: #4caf50;
            font-weight: bold;
        }
        .code-example {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
            font-family: monospace;
            white-space: pre-wrap;
            overflow-x: auto;
        }
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-weight: bold;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .status.warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
    </style>
</head>
<body>
    <h1>Main.vue 用户信息显示和登出功能测试</h1>
    
    <div class="test-section">
        <h2 class="test-title">👤 用户信息显示功能</h2>
        
        <div class="status info">
            ℹ️ 在Main.vue右上角添加了用户信息显示和登出功能
        </div>
        
        <!-- 模拟Header显示 -->
        <div class="header-demo">
            <div class="header-left">
                <span style="font-weight: bold; color: #667eea;">叮咚回收管理系统</span>
            </div>
            <div class="header-right">
                <div class="user-profile">
                    <div class="user-avatar">U</div>
                    <span class="user-name">管理员</span>
                    <span class="dropdown-icon">▼</span>
                </div>
            </div>
        </div>
        
        <h3>✅ 已实现的功能</h3>
        <ul class="feature-list">
            <li><strong>用户头像显示</strong>：显示用户头像或默认头像图标</li>
            <li><strong>用户名显示</strong>：显示用户名（支持多种字段名）</li>
            <li><strong>下拉菜单</strong>：点击用户信息显示下拉菜单</li>
            <li><strong>登出功能</strong>：提供退出登录选项</li>
            <li><strong>确认对话框</strong>：登出前显示确认提示</li>
            <li><strong>登录状态检查</strong>：页面加载时检查登录状态</li>
            <li><strong>自动跳转</strong>：未登录时自动跳转到登录页</li>
        </ul>
    </div>

    <div class="test-section">
        <h2 class="test-title">🔧 技术实现</h2>
        
        <h3>导入的依赖</h3>
        <div class="code-example">import { ref, onMounted, computed } from 'vue'
import { useRouter } from "vue-router"
import { ElMessage, ElMessageBox } from 'element-plus'
import { User, SwitchButton } from '@element-plus/icons-vue'
import { getUserInfo, logout, isLoggedIn } from '@/utils/auth.js'</div>

        <h3>用户信息状态管理</h3>
        <div class="code-example">// 用户信息相关
const userInfo = ref(null)

// 计算属性：获取用户名
const userName = computed(() => {
  return userInfo.value?.username || userInfo.value?.name || '用户'
})

// 计算属性：获取用户头像
const userAvatar = computed(() => {
  return userInfo.value?.avatar || ''
})</div>

        <h3>登出功能实现</h3>
        <div class="code-example">// 处理登出
const handleLogout = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要退出登录吗？',
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )
    
    // 执行登出
    logout()
    ElMessage.success('已成功退出登录')
  } catch (error) {
    // 用户取消登出
    console.log('用户取消登出')
  }
}</div>
    </div>

    <div class="test-section">
        <h2 class="test-title">🎨 UI设计特性</h2>
        
        <h3>用户信息区域设计</h3>
        <ul class="feature-list">
            <li><strong>头像显示</strong>：32px圆形头像，紫色渐变背景</li>
            <li><strong>用户名</strong>：14px字体，深灰色，中等粗细</li>
            <li><strong>下拉箭头</strong>：悬停时旋转180度动画</li>
            <li><strong>悬停效果</strong>：背景色变化，轻微上移</li>
            <li><strong>边框样式</strong>：圆角边框，淡紫色主题</li>
        </ul>

        <h3>响应式设计</h3>
        <ul class="feature-list">
            <li><strong>桌面端</strong>：显示完整用户信息（头像+用户名+箭头）</li>
            <li><strong>平板端</strong>：保持完整显示，调整间距</li>
            <li><strong>手机端</strong>：隐藏用户名，只显示头像和箭头</li>
            <li><strong>小屏手机</strong>：进一步优化布局比例</li>
        </ul>

        <h3>交互体验</h3>
        <ul class="feature-list">
            <li><strong>点击触发</strong>：点击用户信息区域显示下拉菜单</li>
            <li><strong>确认对话框</strong>：登出前显示警告确认框</li>
            <li><strong>成功提示</strong>：登出成功后显示成功消息</li>
            <li><strong>自动跳转</strong>：登出后自动跳转到登录页</li>
        </ul>
    </div>

    <div class="test-section">
        <h2 class="test-title">🔒 安全特性</h2>
        
        <div class="status warning">
            ⚠️ 重要：页面加载时会自动检查登录状态
        </div>
        
        <h3>登录状态检查</h3>
        <div class="code-example">// 组件挂载时检查登录状态和获取用户信息
onMounted(() => {
  // 检查登录状态
  if (!isLoggedIn()) {
    router.push('/login')
    return
  }
  
  // 获取用户信息
  userInfo.value = getUserInfo()
  
  // 如果没有用户信息，跳转到登录页
  if (!userInfo.value) {
    ElMessage.error('用户信息获取失败，请重新登录')
    router.push('/login')
  }
})</div>

        <h3>安全机制</h3>
        <ul class="feature-list">
            <li><strong>Token验证</strong>：检查localStorage中的token</li>
            <li><strong>用户信息验证</strong>：确保用户信息存在且有效</li>
            <li><strong>自动跳转</strong>：未登录时自动跳转到登录页</li>
            <li><strong>错误处理</strong>：用户信息获取失败时显示错误提示</li>
            <li><strong>登出清理</strong>：登出时清理所有认证信息</li>
        </ul>
    </div>

    <div class="test-section">
        <h2 class="test-title">✅ 验证步骤</h2>
        
        <div class="status success">
            🎉 功能已实现！请访问主页面验证效果
        </div>
        
        <ol>
            <li>登录系统后进入主页面</li>
            <li>观察右上角是否显示用户信息（头像+用户名）</li>
            <li>点击用户信息区域，确认下拉菜单正常显示</li>
            <li>点击"退出登录"选项</li>
            <li>确认显示确认对话框</li>
            <li>点击"确定"后验证是否成功登出并跳转到登录页</li>
            <li>测试不同屏幕尺寸下的响应式显示效果</li>
        </ol>
        
        <h3>预期效果</h3>
        <ul class="feature-list">
            <li>✅ 右上角显示用户头像和用户名</li>
            <li>✅ 点击用户信息显示下拉菜单</li>
            <li>✅ 登出前显示确认对话框</li>
            <li>✅ 登出成功后跳转到登录页</li>
            <li>✅ 移动端适配良好</li>
            <li>✅ 悬停效果流畅自然</li>
        </ul>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            console.log('用户信息显示和登出功能测试页面已加载');
            console.log('请访问主页面验证实际效果');
            
            // 模拟用户信息点击
            const userProfile = document.querySelector('.user-profile');
            userProfile.addEventListener('click', function() {
                alert('用户信息点击测试 - 在实际页面中会显示下拉菜单');
            });
        });
    </script>
</body>
</html>
