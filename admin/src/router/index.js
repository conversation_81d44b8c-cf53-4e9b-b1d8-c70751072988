import { createRouter, createWebHistory } from 'vue-router'
import Login from '../views/Login.vue'

import { getToken } from '@/utils/auth.js'
import menus   from "@/router/menu.js";

const routes = [
  {
    path: '/',
    component:  () => import('@/views/Main.vue'),
    redirect: 'dashboard',
    children: [
      {
        name: 'dashboard',
        path: 'dashboard',
        meta: {
          title: '仪表板',
          code: 'dashboard'
        },
        component: () => import('@/views/dashboard/Welcome.vue')
      },
        ...menus
    ]
  },
  {
    path: '/login',
    name: 'login',
    component: Login
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 路由守卫
router.beforeEach((to, from, next) => {
  const token = getToken()
  if (to.path !== '/login' && !token) {
    next('/login')
  } else {
    next()
  }
})

export default router
