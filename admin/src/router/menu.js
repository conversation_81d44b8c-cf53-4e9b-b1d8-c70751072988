const  menus = [
  {
    name: 'system',
    path: 'system',
    redirect: '/system/user',
    meta: {
      title: '系统管理',
      code: 'system'
    },
    children: [{
      name: 'user',
      path: 'user',
      meta: {
        title: '用户管理',
        code: 'user'
      },
      component: () => import('@/views/user/index.vue')
    },{
      name: 'dict',
      path: 'dict',
      meta: {
        title: '数据字典',
        code: 'dict'
      },
      component: () => import('@/views/dict/Dict.vue')
    }
    ]
  },
  {
    name: 'recycle',
    path: 'recycle',
    redirect: '/recycle/order',
    meta: {
      title: '回收管理',
      code: 'recycle'
    },
    children: [{
      name: 'recycleOrder',
      path: 'order',
      meta: {
        title: '订单管理',
        code: 'recycleOrder'
      },
      component: () => import('@/views/recycle/Order.vue')
    }, {
      name: 'recyclePrice',
      path: 'price',
      meta: {
        title: '价格管理',
        code: 'recyclePrice'
      },
      component: () => import('@/views/recycle/RecyclePrice.vue')
    }]
  }
]

export default menus
