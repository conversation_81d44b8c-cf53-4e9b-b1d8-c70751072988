<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>确认人员显示修复测试</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .api-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background-color: #fafafa;
        }
        .api-title {
            font-size: 18px;
            font-weight: bold;
            color: #409eff;
            margin-bottom: 15px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        input, select {
            width: 200px;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        button {
            background-color: #409eff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        button:hover {
            background-color: #66b1ff;
        }
        button.success {
            background-color: #67c23a;
        }
        button.success:hover {
            background-color: #85ce61;
        }
        button.warning {
            background-color: #e6a23c;
        }
        button.warning:hover {
            background-color: #ebb563;
        }
        .result {
            margin-top: 15px;
            padding: 15px;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
        }
        .success {
            background-color: #f0f9ff;
            border: 1px solid #b3e5fc;
            color: #0277bd;
        }
        .error {
            background-color: #fff3e0;
            border: 1px solid #ffcc02;
            color: #f57c00;
        }
        .info {
            background-color: #e8f5e8;
            border: 1px solid #4caf50;
            color: #2e7d32;
        }
        .order-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }
        .order-table th,
        .order-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        .order-table th {
            background-color: #f5f5f5;
            font-weight: bold;
        }
        .status-assigned {
            color: #67c23a;
            font-weight: bold;
        }
        .status-unassigned {
            color: #f56c6c;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 确认人员显示修复测试</h1>
        
        <!-- 获取用户列表 -->
        <div class="api-section">
            <div class="api-title">1. 获取用户列表</div>
            <p>获取系统中的用户，用于分配确认人员</p>
            <button onclick="getAllUsers()" class="success">获取用户列表</button>
            <div id="usersResult" class="result"></div>
        </div>

        <!-- 查询订单列表 -->
        <div class="api-section">
            <div class="api-title">2. 查询订单列表（检查确认人员显示）</div>
            <p>查询订单列表，检查确认人员字段是否正确显示</p>
            <button onclick="queryOrders()">查询订单列表</button>
            <div id="ordersResult" class="result"></div>
            <div id="ordersTable"></div>
        </div>

        <!-- 分配确认人员测试 -->
        <div class="api-section">
            <div class="api-title">3. 分配确认人员测试</div>
            <p>测试分配确认人员功能，验证确认人姓名是否正确设置</p>
            <div class="form-group">
                <label>订单ID:</label>
                <input type="number" id="testOrderId" placeholder="输入订单ID">
            </div>
            <div class="form-group">
                <label>确认人员ID:</label>
                <input type="number" id="testConfirmerId" placeholder="输入确认人员ID">
            </div>
            <button onclick="testAssignConfirmer()" class="warning">测试分配确认人员</button>
            <div id="assignTestResult" class="result"></div>
        </div>

        <!-- 创建测试订单 -->
        <div class="api-section">
            <div class="api-title">4. 创建测试订单</div>
            <p>创建一个测试订单，然后分配确认人员进行测试</p>
            <button onclick="createTestOrder()">创建测试订单</button>
            <div id="createOrderResult" class="result"></div>
        </div>

        <!-- 完整修复测试 -->
        <div class="api-section">
            <div class="api-title">5. 完整修复测试</div>
            <p>执行完整的确认人员显示修复测试流程</p>
            <button onclick="runFullFixTest()" style="background-color: #f56c6c; font-size: 16px; padding: 15px 30px;">
                🚀 执行完整修复测试
            </button>
            <div id="fullFixResult" class="result"></div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8081';
        let usersList = [];
        let testOrderId = null;

        // 通用请求函数
        async function apiRequest(url, options = {}) {
            try {
                const response = await fetch(API_BASE + url, {
                    headers: {
                        'Content-Type': 'application/json',
                        ...options.headers
                    },
                    ...options
                });
                
                const result = await response.json();
                return result;
            } catch (error) {
                return { success: false, msg: '请求失败: ' + error.message };
            }
        }

        // 显示结果
        function showResult(elementId, result, type = 'info') {
            const element = document.getElementById(elementId);
            element.textContent = JSON.stringify(result, null, 2);
            element.className = `result ${type}`;
        }

        // 1. 获取所有用户
        async function getAllUsers() {
            const result = await apiRequest('/api/users?page=0&size=0');
            showResult('usersResult', result, result.success ? 'success' : 'error');
            
            if (result.success && result.data) {
                const users = result.data.content || result.data;
                usersList = users;
                
                if (users.length > 0) {
                    document.getElementById('testConfirmerId').value = users[0].id;
                }
            }
            
            return result;
        }

        // 2. 查询订单列表
        async function queryOrders() {
            const result = await apiRequest('/api/recycle-orders?page=1&size=10');
            showResult('ordersResult', result, result.success ? 'success' : 'error');
            
            if (result.success && result.data && result.data.content) {
                displayOrdersTable(result.data.content);
            }
            
            return result;
        }

        // 显示订单表格
        function displayOrdersTable(orders) {
            const tableContainer = document.getElementById('ordersTable');
            
            if (orders.length === 0) {
                tableContainer.innerHTML = '<p>暂无订单数据</p>';
                return;
            }
            
            let tableHtml = `
                <table class="order-table">
                    <thead>
                        <tr>
                            <th>订单ID</th>
                            <th>订单号</th>
                            <th>联系人</th>
                            <th>确认人员ID</th>
                            <th>确认人员姓名</th>
                            <th>状态</th>
                        </tr>
                    </thead>
                    <tbody>
            `;
            
            orders.forEach(order => {
                const confirmerStatus = order.confirmerName ? 
                    `<span class="status-assigned">${order.confirmerName}</span>` : 
                    `<span class="status-unassigned">未分配</span>`;
                
                tableHtml += `
                    <tr>
                        <td>${order.id}</td>
                        <td>${order.orderNo}</td>
                        <td>${order.contactName}</td>
                        <td>${order.confirmerId || '-'}</td>
                        <td>${confirmerStatus}</td>
                        <td>${order.orderStatusDesc}</td>
                    </tr>
                `;
            });
            
            tableHtml += '</tbody></table>';
            tableContainer.innerHTML = tableHtml;
        }

        // 3. 测试分配确认人员
        async function testAssignConfirmer() {
            const orderId = document.getElementById('testOrderId').value;
            const confirmerId = document.getElementById('testConfirmerId').value;
            
            if (!orderId || !confirmerId) {
                showResult('assignTestResult', { success: false, msg: '请输入订单ID和确认人员ID' }, 'error');
                return;
            }

            const result = await apiRequest('/api/recycle-orders/assign-confirmer', {
                method: 'POST',
                body: JSON.stringify({
                    orderIds: [parseInt(orderId)],
                    confirmerId: parseInt(confirmerId),
                    assignRemark: '测试分配确认人员'
                })
            });
            
            showResult('assignTestResult', result, result.success ? 'success' : 'error');
            
            if (result.success) {
                // 重新查询订单列表验证结果
                setTimeout(() => {
                    queryOrders();
                }, 1000);
            }
            
            return result;
        }

        // 4. 创建测试订单
        async function createTestOrder() {
            const testOrder = {
                contactName: '测试用户' + Math.floor(Math.random() * 1000),
                contactPhone: '138' + Math.floor(Math.random() * 100000000).toString().padStart(8, '0'),
                province: '广东省',
                city: '深圳市',
                district: '南山区',
                detailedAddress: '科技园南区深圳湾科技生态园测试栋',
                itemCategory: '废纸',
                estimatedWeight: Math.floor(Math.random() * 50) + 10,
                appointmentDate: '2024-12-25',
                appointmentTimeSlot: '上午',
                remark: '测试订单，用于确认人员显示修复测试'
            };

            const result = await apiRequest('/api/recycle-orders', {
                method: 'POST',
                body: JSON.stringify(testOrder)
            });
            
            showResult('createOrderResult', result, result.success ? 'success' : 'error');
            
            if (result.success) {
                testOrderId = result.data.id;
                document.getElementById('testOrderId').value = testOrderId;
            }
            
            return result;
        }

        // 5. 完整修复测试
        async function runFullFixTest() {
            const resultDiv = document.getElementById('fullFixResult');
            resultDiv.textContent = '开始执行完整修复测试...\n';
            resultDiv.className = 'result info';

            try {
                // 步骤1: 获取用户列表
                resultDiv.textContent += '\n步骤1: 获取用户列表...\n';
                const usersResult = await getAllUsers();
                if (!usersResult.success) {
                    throw new Error('获取用户列表失败: ' + usersResult.msg);
                }
                resultDiv.textContent += `✅ 用户列表获取成功，共 ${usersList.length} 个用户\n`;

                // 步骤2: 创建测试订单
                resultDiv.textContent += '\n步骤2: 创建测试订单...\n';
                const createResult = await createTestOrder();
                if (!createResult.success) {
                    throw new Error('创建测试订单失败: ' + createResult.msg);
                }
                resultDiv.textContent += `✅ 测试订单创建成功，订单ID: ${testOrderId}\n`;

                // 等待1秒
                await new Promise(resolve => setTimeout(resolve, 1000));

                // 步骤3: 分配确认人员
                resultDiv.textContent += '\n步骤3: 分配确认人员...\n';
                const assignResult = await testAssignConfirmer();
                if (!assignResult.success) {
                    throw new Error('分配确认人员失败: ' + assignResult.msg);
                }
                resultDiv.textContent += '✅ 确认人员分配成功\n';

                // 等待1秒
                await new Promise(resolve => setTimeout(resolve, 1000));

                // 步骤4: 验证订单列表显示
                resultDiv.textContent += '\n步骤4: 验证订单列表显示...\n';
                const ordersResult = await queryOrders();
                if (!ordersResult.success) {
                    throw new Error('查询订单列表失败: ' + ordersResult.msg);
                }
                
                // 检查测试订单的确认人员是否正确显示
                const testOrder = ordersResult.data.content.find(order => order.id === testOrderId);
                if (testOrder && testOrder.confirmerName) {
                    resultDiv.textContent += `✅ 确认人员显示正确: ${testOrder.confirmerName}\n`;
                } else {
                    throw new Error('确认人员显示仍然有问题');
                }

                resultDiv.textContent += '\n🎉 完整修复测试成功！\n';
                resultDiv.textContent += '\n📋 测试结果总结:\n';
                resultDiv.textContent += `- 测试订单ID: ${testOrderId}\n`;
                resultDiv.textContent += `- 确认人员: ${testOrder.confirmerName}\n`;
                resultDiv.textContent += `- 确认人员ID: ${testOrder.confirmerId}\n`;
                resultDiv.textContent += '- 确认人员显示: ✅ 正常\n';

                resultDiv.className = 'result success';

            } catch (error) {
                resultDiv.textContent += `\n❌ 测试失败: ${error.message}\n`;
                resultDiv.className = 'result error';
            }
        }

        // 页面加载时自动获取用户列表和订单列表
        window.addEventListener('load', () => {
            getAllUsers();
            queryOrders();
        });
    </script>
</body>
</html>
