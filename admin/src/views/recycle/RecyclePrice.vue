<template>
  <div class="recycle-price-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>商品回收价格管理</h2>
      <p>管理各类回收商品的价格信息</p>
    </div>

    <!-- 搜索和操作区域 -->
    <el-card class="search-card" shadow="never">

      <!-- 操作按钮 -->
      <div class="action-buttons">
        <el-button type="primary" @click="handleAdd">
          <el-icon><Plus /></el-icon>
          新增价格
        </el-button>
      </div>
    </el-card>

    <!-- 数据表格 -->
    <el-card class="table-card" shadow="never">
      <el-table
        :data="tableData"
        v-loading="loading"
        @selection-change="handleSelectionChange"
        stripe
        border
        style="width: 100%"
      >
        <el-table-column type="selection" width="55" />

        <el-table-column prop="unitPrice" label="单价" width="100" align="right">
          <template #default="{ row }">
            <span class="price-text">¥{{ row.unitPrice }}</span>
          </template>
        </el-table-column>

        <el-table-column prop="effectiveDate" label="生效日期" width="160" />

        <el-table-column prop="expiryDate" label="失效日期" width="160" >
          <template #default="{ row }">
            <span v-if="row.expiryDate">{{row.expiryDate}}</span>
            <span style="color: #ed7f7f" v-else>长期有效</span>
          </template>
        </el-table-column>

        <el-table-column prop="isCurrent" label="当前价格" width="100" align="center">
          <template #default="{ row }">
            <el-tag :type="row.isCurrent === '1' ? 'success' : 'info'" size="small">
              {{ row.isCurrent === '1' ? '是' : '否' }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="state" label="状态" width="80" align="center">
          <template #default="{ row }">
            <el-tag :type="row.state === '1' ? 'success' : 'danger'" size="small">
              {{ row.state === '1' ? '有效' : '无效' }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="creator" label="创建人" width="100" />

        <el-table-column prop="createDate" label="创建时间" width="180">
          <template #default="{ row }">
            {{ formatDateTime(row.createDate) }}
          </template>
        </el-table-column>

        <el-table-column label="操作" :width="isMobile ? 120 : 200" fixed="right" align="center">
          <template #default="{ row }">
            <template v-if="!isMobile">
              <el-button type="primary" size="small" @click="handleEdit(row)">
                编辑
              </el-button>
              <el-button
                :type="row.state === '1' ? 'warning' : 'success'"
                size="small"
                :disabled="row.isCurrent === '1'"
                @click="handleToggleState(row)"
              >
                {{ row.state === '1' ? '禁用' : '启用' }}
              </el-button>
            </template>
            <template v-else>
              <el-dropdown trigger="click" @command="(cmd) => handleMobileAction(row, cmd)">
                <el-button type="primary" size="small">
                  操作<el-icon class="el-icon--right"><ArrowDown /></el-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item command="edit">编辑</el-dropdown-item>
                    <el-dropdown-item
                      command="toggle"
                      :disabled="row.isCurrent === '1'"
                    >
                      {{ row.state === '1' ? '禁用' : '启用' }}
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </template>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          :layout="isMobile ? 'total, prev, next' : 'total, sizes, prev, pager, next, jumper'"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 新增/编辑对话框 -->
    <el-dialog
      :title="dialogTitle"
      v-model="dialogVisible"
      :width="isMobile ? '96%' : '600px'"
      :close-on-click-modal="false"
    >
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="100px"
      >
        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="单价" prop="unitPrice">
              <el-input-number
                v-model="formData.unitPrice"
                :precision="1"
                :min="0"
                :max="99999.99"
                placeholder="请输入单价"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="当前价格">
              <el-tag :type="formData.isCurrent === '1' ? 'success' : 'info'" size="small">
                {{ formData.isCurrent === '1' ? '是' : '否' }}
              </el-tag>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="生效日期" prop="effectiveDate">
              <el-date-picker
                  :disabled="formData.isCurrent === '1'"
                v-model="formData.effectiveDate"
                type="date"
                placeholder="请选择生效日期"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24" v-if="formData.id">
            <el-form-item label="失效日期" prop="expiryDate">
              <el-date-picker
                  :disabled="formData.isCurrent === '1'"
                v-model="formData.expiryDate"
                type="date"
                placeholder="请选择失效日期"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit" :loading="submitLoading">
            确定
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onBeforeUnmount, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, Refresh, Plus, Delete, View, ArrowDown } from '@element-plus/icons-vue'
import { recyclePriceApi } from '@/api/recyclePrice'

// 响应式数据
const loading = ref(false)
const submitLoading = ref(false)
const dialogVisible = ref(false)
const dialogTitle = ref('')
const isEdit = ref(false)
const currentEditId = ref(null)

// 表格数据
const tableData = ref([])
const selectedRows = ref([])

// 分页数据
const pagination = reactive({
  page: 1,
  size: 10,
  total: 0
})

// 搜索表单
const searchForm = reactive({
  itemType: '',
  itemName: '',
  state: '',
  currentOnly: null,
  dateRange: []
})

// 物品类型和名称选项
const itemTypes = ref([])
const itemNames = ref([])

// 表单数据
const formData = reactive({
  id: null,
  itemType: '',
  itemName: '',
  unitPrice: null,
  priceUnit: '元/公斤',
  effectiveDate: '',
  expiryDate: '',
  isCurrent: '0',
  state: '1',
  remark: ''
})

// 表单引用
const formRef = ref()

// 表单验证规则
const formRules = {
  itemType: [
    { required: true, message: '请输入物品类型', trigger: 'blur' }
  ],
  itemName: [
    { required: true, message: '请输入物品名称', trigger: 'blur' }
  ],
  unitPrice: [
    { required: true, message: '请输入单价', trigger: 'blur' },
    { type: 'number', min: 0, max: 99999.99, message: '单价必须在0-99999.99之间', trigger: 'blur' }
  ],
  priceUnit: [
    { required: true, message: '请选择价格单位', trigger: 'change' }
  ],
  effectiveDate: [
    { required: true, message: '请选择生效日期', trigger: 'change' }
  ]
}

// 页面加载时初始化
onMounted(() => {
  loadData()
  loadItemTypes()
})

// 加载数据
const loadData = async () => {
  loading.value = true
  try {
    const queryParams = {
      ...searchForm,
      startDate: searchForm.dateRange?.[0] || null,
      endDate: searchForm.dateRange?.[1] || null
    }
    delete queryParams.dateRange

    const response = await recyclePriceApi.searchPrices(queryParams, pagination.page - 1, pagination.size)
    if (response.success) {
      tableData.value = response.data.content || []
      pagination.total = response.data.totalElements || 0
    } else {
      ElMessage.error(response.msg || '加载数据失败')
    }
  } catch (error) {
    console.error('加载数据失败:', error)
    ElMessage.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

// 加载物品类型
const loadItemTypes = async () => {
  try {
    const response = await recyclePriceApi.getAllItemTypes()
    if (response.success) {
      itemTypes.value = response.data || []
    }
  } catch (error) {
    console.error('加载物品类型失败:', error)
  }
}

// 物品类型变化时加载对应的物品名称
const onItemTypeChange = async () => {
  searchForm.itemName = ''
  itemNames.value = []

  if (searchForm.itemType) {
    try {
      const response = await recyclePriceApi.getItemNamesByType(searchForm.itemType)
      if (response.success) {
        itemNames.value = response.data || []
      }
    } catch (error) {
      console.error('加载物品名称失败:', error)
    }
  }
}

// 搜索
const handleSearch = () => {
  pagination.page = 1
  loadData()
}

// 重置搜索
const handleReset = () => {
  Object.assign(searchForm, {
    itemType: '',
    itemName: '',
    state: '',
    currentOnly: null,
    dateRange: []
  })
  itemNames.value = []
  pagination.page = 1
  loadData()
}

// 加载当前有效价格
const loadCurrentPrices = async () => {
  loading.value = true
  try {
    const response = await recyclePriceApi.getCurrentPrices()
    if (response.success) {
      tableData.value = response.data || []
      pagination.total = response.data.length || 0
      ElMessage.success('已显示所有当前有效价格')
    } else {
      ElMessage.error(response.msg || '加载当前价格失败')
    }
  } catch (error) {
    console.error('加载当前价格失败:', error)
    ElMessage.error('加载当前价格失败')
  } finally {
    loading.value = false
  }
}

// 表格选择变化
const handleSelectionChange = (selection) => {
  selectedRows.value = selection
}

// 分页大小变化
const handleSizeChange = (size) => {
  pagination.size = size
  pagination.page = 1
  loadData()
}

// 当前页变化
const handleCurrentChange = (page) => {
  pagination.page = page
  loadData()
}

// 新增
const handleAdd = () => {
  isEdit.value = false
  dialogTitle.value = '新增回收价格'
  resetFormData()
  dialogVisible.value = true
}

// 编辑
const handleEdit = (row) => {
  isEdit.value = true
  dialogTitle.value = '编辑回收价格'
  currentEditId.value = row.id

  // 填充表单数据
  Object.assign(formData, {
    id: row.id,
    itemType: row.itemType,
    itemName: row.itemName,
    unitPrice: row.unitPrice,
    priceUnit: row.priceUnit,
    effectiveDate: row.effectiveDate,
    expiryDate: row.expiryDate,
    isCurrent: row.isCurrent,
    state: row.state,
    remark: row.remark
  })

  dialogVisible.value = true
}

// 重置表单数据
const resetFormData = () => {
  Object.assign(formData, {
    id: null,
    itemType: '',
    itemName: '',
    unitPrice: null,
    priceUnit: '元/公斤',
    effectiveDate: '',
    expiryDate: '',
    isCurrent: '0',
    state: '1',
    remark: ''
  })

  nextTick(() => {
    formRef.value?.clearValidate()
  })
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()

    submitLoading.value = true

    let response
    if (isEdit.value) {
      response = await recyclePriceApi.updatePrice(currentEditId.value, formData)
    } else {
      response = await recyclePriceApi.createPrice(formData)
    }

    if (response.success) {
      ElMessage.success(isEdit.value ? '更新成功' : '创建成功')
      dialogVisible.value = false
      loadData()
    } else {
      ElMessage.error(response.msg || (isEdit.value ? '更新失败' : '创建失败'))
    }
  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error('提交失败')
  } finally {
    submitLoading.value = false
  }
}

// 删除
const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除物品"${row.itemType} - ${row.itemName}"的价格信息吗？`,
      '删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const response = await recyclePriceApi.deletePrice(row.id)
    if (response.success) {
      ElMessage.success('删除成功')
      loadData()
    } else {
      ElMessage.error(response.msg || '删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

// 批量删除
const handleBatchDelete = async () => {
  if (!selectedRows.value.length) {
    ElMessage.warning('请选择要删除的数据')
    return
  }

  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedRows.value.length} 条价格信息吗？`,
      '批量删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const ids = selectedRows.value.map(row => row.id)
    const response = await recyclePriceApi.deletePrices(ids)
    if (response.success) {
      ElMessage.success('批量删除成功')
      loadData()
    } else {
      ElMessage.error(response.msg || '批量删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量删除失败:', error)
      ElMessage.error('批量删除失败')
    }
  }
}

// 设置为当前价格
const handleSetCurrent = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要将"${row.itemType} - ${row.itemName}"设置为当前有效价格吗？`,
      '设置确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'info'
      }
    )

    const response = await recyclePriceApi.setAsCurrentPrice(row.id)
    if (response.success) {
      ElMessage.success('设置成功')
      loadData()
    } else {
      ElMessage.error(response.msg || '设置失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('设置失败:', error)
      ElMessage.error('设置失败')
    }
  }
}

// 切换状态（启用/禁用）
const handleToggleState = async (row) => {
  const action = row.state === '1' ? '禁用' : '启用'

  try {
    await ElMessageBox.confirm(
      `确定要${action}"${row.effectiveDate} - ${row.expiryDate? row.expiryDate: '长期'}"的价格信息吗？`,
      `${action}确认`,
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    let response
    if (row.state === '1') {
      response = await recyclePriceApi.disablePrice(row.id)
    } else {
      response = await recyclePriceApi.enablePrice(row.id)
    }

    if (response.success) {
      ElMessage.success(`${action}成功`)
      loadData()
    } else {
      ElMessage.error(response.msg || `${action}失败`)
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error(`${action}失败:`, error)
      ElMessage.error(`${action}失败`)
    }
  }
}

// 格式化日期时间
const formatDateTime = (dateTime) => {
  if (!dateTime) return ''
  return new Date(dateTime).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// ============== 移动端适配 ==============
const isMobile = ref(false)
const updateIsMobile = () => {
  isMobile.value = window.innerWidth <= 768
}
onMounted(() => {
  updateIsMobile()
  window.addEventListener('resize', updateIsMobile)
})
onBeforeUnmount(() => {
  window.removeEventListener('resize', updateIsMobile)
})

// 移动端折叠菜单统一处理
const handleMobileAction = (row, cmd) => {
  switch (cmd) {
    case 'edit':
      handleEdit(row)
      break
    case 'toggle':
      handleToggleState(row)
      break
  }
}
</script>

<style scoped>
.recycle-price-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 60px);
  overflow-y: auto;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  color: #303133;
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
}

.page-header p {
  color: #909399;
  margin: 0;
  font-size: 14px;
}

.search-card {
  margin-bottom: 20px;
}

.search-form {
  margin-bottom: 20px;
}

.action-buttons {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.table-card {
  background: white;
  border-radius: 8px;
  overflow: hidden;
}

.price-text {
  color: #e6a23c;
  font-weight: 600;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
  padding: 20px 0;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .recycle-price-container {
    padding: 10px;
  }

  .search-form {
    flex-direction: column;
  }

  .search-form .el-form-item {
    margin-right: 0;
    margin-bottom: 15px;
  }

  .action-buttons {
    justify-content: center;
  }

  .el-table {
    font-size: 12px;
  }

  /* 表格在移动端更紧凑 */
  :deep(.el-table .el-table__cell) {
    padding: 8px 0;
  }
}

/* 表格样式优化 */
:deep(.el-table) {
  border-radius: 8px;
  overflow: hidden;
}

:deep(.el-table th) {
  background-color: #fafafa;
  color: #606266;
  font-weight: 600;
}

:deep(.el-table td) {
  border-bottom: 1px solid #ebeef5;
}

:deep(.el-table tr:hover > td) {
  background-color: #f5f7fa;
}

/* 标签样式 */
:deep(.el-tag) {
  border-radius: 4px;
  font-weight: 500;
}

/* 按钮样式 */
:deep(.el-button) {
  border-radius: 6px;
  font-weight: 500;
}

:deep(.el-button--small) {
  padding: 5px 12px;
  font-size: 12px;
}

/* 对话框样式 */
:deep(.el-dialog) {
  border-radius: 12px;
  overflow: hidden;
}

:deep(.el-dialog__header) {
  background-color: #fafafa;
  padding: 20px 24px 16px;
  border-bottom: 1px solid #ebeef5;
}

:deep(.el-dialog__body) {
  padding: 24px;
}

:deep(.el-dialog__footer) {
  padding: 16px 24px 24px;
  border-top: 1px solid #ebeef5;
}

/* 表单样式 */
:deep(.el-form-item__label) {
  font-weight: 500;
  color: #606266;
}

:deep(.el-input__wrapper) {
  border-radius: 6px;
}

:deep(.el-select .el-input__wrapper) {
  border-radius: 6px;
}

:deep(.el-date-editor.el-input) {
  border-radius: 6px;
}

/* 卡片样式 */
:deep(.el-card) {
  border-radius: 12px;
  border: 1px solid #ebeef5;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

:deep(.el-card__body) {
  padding: 24px;
}

/* 分页样式 */
:deep(.el-pagination) {
  justify-content: center;
}

:deep(.el-pagination .el-pager li) {
  border-radius: 6px;
  margin: 0 2px;
}

:deep(.el-pagination .btn-prev),
:deep(.el-pagination .btn-next) {
  border-radius: 6px;
}
</style>
