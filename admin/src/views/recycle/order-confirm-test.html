<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>订单确认功能测试</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .api-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background-color: #fafafa;
        }
        .api-title {
            font-size: 18px;
            font-weight: bold;
            color: #409eff;
            margin-bottom: 15px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        input, textarea, select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            box-sizing: border-box;
        }
        .form-row {
            display: flex;
            gap: 15px;
        }
        .form-row .form-group {
            flex: 1;
        }
        button {
            background-color: #409eff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        button:hover {
            background-color: #66b1ff;
        }
        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .result {
            margin-top: 15px;
            padding: 15px;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
        .success {
            background-color: #f0f9ff;
            border: 1px solid #b3e5fc;
            color: #0277bd;
        }
        .error {
            background-color: #fff3e0;
            border: 1px solid #ffcc02;
            color: #f57c00;
        }
        .info {
            background-color: #e8f5e8;
            border: 1px solid #4caf50;
            color: #2e7d32;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>✅ 订单确认功能测试</h1>
        
        <!-- 分配确认人 -->
        <div class="api-section">
            <div class="api-title">1. 分配确认人</div>
            <div class="form-row">
                <div class="form-group">
                    <label>订单ID列表 (逗号分隔):</label>
                    <input type="text" id="assignOrderIds" placeholder="如: 1,2,3" value="1,2">
                </div>
                <div class="form-group">
                    <label>确认人ID:</label>
                    <input type="number" id="assignConfirmerId" placeholder="输入确认人ID" value="2">
                </div>
            </div>
            <div class="form-group">
                <label>分配备注:</label>
                <textarea id="assignRemark" placeholder="输入分配备注（可选）">请及时确认订单信息</textarea>
            </div>
            <button onclick="assignConfirmer()">分配确认人</button>
            <div id="assignResult" class="result"></div>
        </div>

        <!-- 确认订单 -->
        <div class="api-section">
            <div class="api-title">2. 确认订单</div>
            <div class="form-row">
                <div class="form-group">
                    <label>订单ID:</label>
                    <input type="number" id="confirmOrderId" placeholder="输入订单ID" value="1">
                </div>
                <div class="form-group">
                    <label>确认重量(kg):</label>
                    <input type="number" id="confirmWeight" placeholder="输入确认重量" value="12.50" step="0.01">
                </div>
            </div>
            <div class="form-row">
                <div class="form-group">
                    <label>交易金额(元):</label>
                    <input type="number" id="transactionAmount" placeholder="输入交易金额" value="125.00" step="0.01">
                </div>
            </div>
            <div class="form-group">
                <label>确认备注:</label>
                <textarea id="confirmRemark" placeholder="输入确认备注（可选）">实际重量与预估重量基本一致，交易完成</textarea>
            </div>
            <button onclick="confirmOrder()">确认订单</button>
            <div id="confirmResult" class="result"></div>
        </div>

        <!-- 查询待确认订单 -->
        <div class="api-section">
            <div class="api-title">3. 查询待确认订单</div>
            <div class="form-group">
                <label>确认人ID:</label>
                <input type="number" id="queryConfirmerId" placeholder="输入确认人ID" value="2">
            </div>
            <button onclick="getPendingConfirmOrders()">查询待确认订单</button>
            <div id="queryResult" class="result"></div>
        </div>

        <!-- 查询订单详情 -->
        <div class="api-section">
            <div class="api-title">4. 查询订单详情（验证新字段）</div>
            <div class="form-group">
                <label>订单ID:</label>
                <input type="number" id="detailOrderId" placeholder="输入订单ID" value="1">
            </div>
            <button onclick="getOrderDetail()">查询订单详情</button>
            <div id="detailResult" class="result"></div>
        </div>

        <!-- 创建测试订单 -->
        <div class="api-section">
            <div class="api-title">5. 创建测试订单</div>
            <p>为了测试确认功能，可以先创建一些测试订单</p>
            <button onclick="createTestOrder()">创建测试订单</button>
            <div id="createTestResult" class="result"></div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8081';

        // 通用请求函数
        async function apiRequest(url, options = {}) {
            try {
                const response = await fetch(API_BASE + url, {
                    headers: {
                        'Content-Type': 'application/json',
                        ...options.headers
                    },
                    ...options
                });
                
                const result = await response.json();
                return result;
            } catch (error) {
                return { success: false, msg: '请求失败: ' + error.message };
            }
        }

        // 显示结果
        function showResult(elementId, result, type = 'info') {
            const element = document.getElementById(elementId);
            element.textContent = JSON.stringify(result, null, 2);
            element.className = `result ${type}`;
        }

        // 1. 分配确认人
        async function assignConfirmer() {
            const orderIdsStr = document.getElementById('assignOrderIds').value;
            const confirmerId = document.getElementById('assignConfirmerId').value;
            const assignRemark = document.getElementById('assignRemark').value;
            
            if (!orderIdsStr || !confirmerId) {
                showResult('assignResult', { success: false, msg: '请输入订单ID列表和确认人ID' }, 'error');
                return;
            }

            const orderIds = orderIdsStr.split(',').map(id => parseInt(id.trim())).filter(id => !isNaN(id));
            
            if (orderIds.length === 0) {
                showResult('assignResult', { success: false, msg: '订单ID格式不正确' }, 'error');
                return;
            }

            const result = await apiRequest('/api/recycle-orders/assign-confirmer', {
                method: 'POST',
                body: JSON.stringify({
                    orderIds: orderIds,
                    confirmerId: parseInt(confirmerId),
                    assignRemark: assignRemark
                })
            });
            
            showResult('assignResult', result, result.success ? 'success' : 'error');
        }

        // 2. 确认订单
        async function confirmOrder() {
            const orderId = document.getElementById('confirmOrderId').value;
            const confirmWeight = document.getElementById('confirmWeight').value;
            const transactionAmount = document.getElementById('transactionAmount').value;
            const confirmRemark = document.getElementById('confirmRemark').value;
            
            if (!orderId || !confirmWeight || !transactionAmount) {
                showResult('confirmResult', { success: false, msg: '请输入订单ID、确认重量和交易金额' }, 'error');
                return;
            }

            const result = await apiRequest('/api/recycle-orders/confirm', {
                method: 'POST',
                body: JSON.stringify({
                    orderId: parseInt(orderId),
                    confirmWeight: parseFloat(confirmWeight),
                    transactionAmount: parseFloat(transactionAmount),
                    confirmRemark: confirmRemark
                })
            });
            
            showResult('confirmResult', result, result.success ? 'success' : 'error');
        }

        // 3. 查询待确认订单
        async function getPendingConfirmOrders() {
            const confirmerId = document.getElementById('queryConfirmerId').value;
            
            if (!confirmerId) {
                showResult('queryResult', { success: false, msg: '请输入确认人ID' }, 'error');
                return;
            }

            const result = await apiRequest(`/api/recycle-orders/pending-confirm/${confirmerId}`);
            showResult('queryResult', result, result.success ? 'success' : 'error');
        }

        // 4. 查询订单详情
        async function getOrderDetail() {
            const orderId = document.getElementById('detailOrderId').value;
            
            if (!orderId) {
                showResult('detailResult', { success: false, msg: '请输入订单ID' }, 'error');
                return;
            }

            const result = await apiRequest(`/api/recycle-orders/${orderId}`);
            showResult('detailResult', result, result.success ? 'success' : 'error');
        }

        // 5. 创建测试订单
        async function createTestOrder() {
            const testOrder = {
                contactName: '测试用户' + Math.floor(Math.random() * 1000),
                contactPhone: '138' + Math.floor(Math.random() * 100000000).toString().padStart(8, '0'),
                province: '广东省',
                city: '深圳市',
                district: '南山区',
                detailedAddress: '科技园南区深圳湾科技生态园' + Math.floor(Math.random() * 100) + '栋',
                itemCategory: ['废纸', '废塑料', '废金属', '电子废料'][Math.floor(Math.random() * 4)],
                estimatedWeight: Math.floor(Math.random() * 100) + 1,
                appointmentDate: '2024-12-25',
                appointmentTimeSlot: ['上午', '下午', '晚上'][Math.floor(Math.random() * 3)],
                remark: '测试订单，用于确认功能测试'
            };

            const result = await apiRequest('/api/recycle-orders', {
                method: 'POST',
                body: JSON.stringify(testOrder)
            });
            
            showResult('createTestResult', result, result.success ? 'success' : 'error');
            
            if (result.success) {
                // 自动填充订单ID到确认表单
                document.getElementById('confirmOrderId').value = result.data.id;
                document.getElementById('detailOrderId').value = result.data.id;
            }
        }
    </script>
</body>
</html>
