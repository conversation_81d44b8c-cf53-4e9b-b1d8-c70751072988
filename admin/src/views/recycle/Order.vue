<template>
  <div class="order-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>回收订单管理</h2>
      <p>管理物品回收订单信息</p>
    </div>

    <!-- 搜索和操作区域 -->
    <el-card class="search-card" shadow="never">
      <el-form :model="searchForm" :inline="true" class="search-form" label-width="80">
        <el-form-item label="订单号">
          <el-input
            v-model="searchForm.orderNo"
            placeholder="请输入订单号"
            clearable
          />
        </el-form-item>

        <el-form-item label="联系人">
          <el-input
            v-model="searchForm.contactName"
            placeholder="请输入联系人"
            clearable
          />
        </el-form-item>

        <el-form-item label="联系电话">
          <el-input
            v-model="searchForm.contactPhone"
            placeholder="请输入联系电话"
            clearable
          />
        </el-form-item>

        <el-form-item label="物品种类">
          <el-input
            v-model="searchForm.itemCategory"
            placeholder="请输入物品种类"
            clearable
          />
        </el-form-item>

        <el-form-item label="订单状态">
          <el-select v-model="searchForm.orderStatus" placeholder="请选择状态" style="width: 150px" clearable>
            <el-option :label="name" v-for="(name, code) in statusMap" :value="code" />
          </el-select>
        </el-form-item>

        <el-form-item label="预约时段">
          <el-select v-model="searchForm.appointmentTimeSlot" placeholder="请选择时间段" style="width: 150px" clearable>
            <el-option label="上午" value="上午" />
            <el-option label="下午" value="下午" />
          </el-select>
        </el-form-item>

        <el-form-item label="预约日期">
          <el-date-picker
            v-model="searchForm.appointmentDateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            style="width: 200px"
          />
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="handleSearch" :loading="loading">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>

      <!-- 操作按钮 -->
      <div class="action-buttons">
        <el-button
          type="primary"
          :disabled="!selectedRows.length"
          @click="handleBatchAssign"
        >
          <el-icon><User /></el-icon>
          指派跑腿员
        </el-button>
      </div>
    </el-card>

    <!-- 数据表格 -->
    <el-card class="table-card" shadow="never">
      <el-table
        :data="tableData"
        v-loading="loading"
        @selection-change="handleSelectionChange"
        stripe
        border
        style="width: 100%"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="orderNo" label="订单号" width="150" />

        <el-table-column prop="contactName" label="联系人" width="100" />

        <el-table-column prop="contactPhone" label="联系电话" width="120" />

        <el-table-column label="地址" show-overflow-tooltip min-width="120">
          <template #default="{ row }">
            {{ `${row.province == row.city ? '' : row.province}${row.city}${row.district}` }}{{row.detailedAddress}}
          </template>
        </el-table-column>

        <el-table-column prop="itemCategory" label="物品种类" width="120" />

        <el-table-column prop="estimatedWeight" label="预估重量" width="120" align="right">
          <template #default="{ row }">
            <span v-if="row.estimatedWeight">{{ row.estimatedWeight }}</span>
            <span v-else class="text-muted">-</span>
          </template>
        </el-table-column>

        <el-table-column prop="orderPrice" label="实时单价" width="100" />
        <el-table-column prop="appointmentDate" label="预约日期" width="120" />

        <el-table-column prop="appointmentTimeSlot" label="时间段" width="80" />

        <el-table-column prop="orderStatus" label="状态" width="100" align="center">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.orderStatus)" size="small">
              {{ getStatusText(row.orderStatus) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="confirmerName" label="跑腿员" width="120" align="center">
          <template #default="{ row }">
            <span v-if="row.confirmerName" class="assigned-user">{{ row.confirmerName }}</span>
            <span v-else class="text-muted">未指派</span>
          </template>
        </el-table-column>

        <el-table-column prop="createDate" label="创建时间" width="160">
          <template #default="{ row }">
            {{ formatDateTime(row.createDate) }}
          </template>
        </el-table-column>

        <el-table-column label="操作" :width="isMobile ? 120 : 200" fixed="right" align="center">
          <template #default="{ row }">
            <template v-if="!isMobile">
              <el-button type="primary" size="small" @click="handleView(row)">
                查看
              </el-button>
              <el-button type="warning" size="small" @click="handleAssign(row)" v-if="row.orderStatus === 1">
                指派跑腿员
              </el-button>
            </template>
            <template v-else>
              <el-dropdown trigger="click" @command="(cmd) => handleMobileAction(row, cmd)">
                <el-button type="primary" size="small">
                  操作<el-icon class="el-icon--right"><ArrowDown /></el-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item command="view">查看</el-dropdown-item>
                    <el-dropdown-item command="assign" v-if="row.orderStatus === 1">指派跑腿员</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </template>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          :layout="isMobile ? 'total, prev, next' : 'total, sizes, prev, pager, next, jumper'"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 指派跑腿员对话框 -->
    <el-dialog
      title="指派跑腿员"
      v-model="assignVisible"
      :width="isMobile ? '96%' : '600px'"
      :close-on-click-modal="false"
    >
      <div class="assign-content">
        <div v-if="isBatchAssign" class="batch-info">
          <el-alert
            :title="`已选择 ${selectedRows.length} 个订单进行批量指派跑腿员`"
            type="info"
            show-icon
            :closable="false"
          />
        </div>

        <div v-else class="single-info">
          <el-descriptions :column="isMobile ? 1 : 2" border size="small">
            <el-descriptions-item label="订单号">{{ currentAssignOrder?.orderNo }}</el-descriptions-item>
            <el-descriptions-item label="联系人">{{ currentAssignOrder?.contactName }}</el-descriptions-item>
            <el-descriptions-item label="联系电话">{{ currentAssignOrder?.contactPhone }}</el-descriptions-item>
            <el-descriptions-item label="物品种类">{{ currentAssignOrder?.itemCategory }}</el-descriptions-item>
          </el-descriptions>
        </div>

        <div class="assign-form">
          <el-form :model="assignForm" label-width="120px">
            <el-form-item label="选择跑腿员" required>
              <el-select
                v-model="assignForm.confirmer"
                placeholder="请选择跑腿员"
                filterable
                style="width: 100%"
              >
                <el-option
                  v-for="user in userList"
                  :key="user.userNo"
                  :label="`${user.nickName || user.username} (${user.userNo || 'N/A'})`"
                  :value="user.userNo"
                />
              </el-select>
            </el-form-item>

            <el-form-item label="指派备注">
              <el-input
                v-model="assignForm.assignRemark"
                type="textarea"
                :rows="3"
                placeholder="请输入指派备注（可选）"
              />
            </el-form-item>
          </el-form>
        </div>
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="assignVisible = false">取消</el-button>
          <el-button type="primary" @click="handleAssignSubmit" :loading="assignLoading">
            确定指派跑腿员
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 订单详情对话框 -->
    <el-dialog
      title="订单详情"
      v-model="detailVisible"
      :width="isMobile ? '96%' : '800px'"
    >
      <div class="order-detail">
        <el-descriptions :column="isMobile ? 1 : 2" border>
          <el-descriptions-item label="订单号">{{ detailData.orderNo }}</el-descriptions-item>
          <el-descriptions-item label="订单状态">
            <el-tag :type="getStatusType(detailData.orderStatus)" size="small">
              {{ getStatusText(detailData.orderStatus) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="联系人">{{ detailData.contactName }}</el-descriptions-item>
          <el-descriptions-item label="联系电话">{{ detailData.contactPhone }}</el-descriptions-item>
          <el-descriptions-item label="省份">{{ detailData.province }}</el-descriptions-item>
          <el-descriptions-item label="城市">{{ detailData.city }}</el-descriptions-item>
          <el-descriptions-item label="区县">{{ detailData.district }}</el-descriptions-item>
          <el-descriptions-item label="物品种类">{{ detailData.itemCategory }}</el-descriptions-item>
          <el-descriptions-item label="详细地址" :span="2">{{ detailData.detailedAddress }}</el-descriptions-item>
          <el-descriptions-item label="预约日期">{{ detailData.appointmentDate }}</el-descriptions-item>
          <el-descriptions-item label="预约时间段">{{ detailData.appointmentTimeSlot }}</el-descriptions-item>
          <el-descriptions-item label="预估重量">
            {{ detailData.estimatedWeight ? detailData.estimatedWeight + ' kg' : '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="跑腿员">{{ detailData.confirmerName || '-' }}</el-descriptions-item>
          <el-descriptions-item label="确认重量">
            {{ detailData.confirmWeight ? detailData.confirmWeight + ' kg' : '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="交易金额">
            {{ detailData.transactionAmount ? '¥' + detailData.transactionAmount : '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="确认时间">{{ formatDateTime(detailData.confirmDate) }}</el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ formatDateTime(detailData.createDate) }}</el-descriptions-item>
          <el-descriptions-item label="修改时间">{{ formatDateTime(detailData.updateDate) }}</el-descriptions-item>
          <el-descriptions-item label="备注" :span="2">{{ detailData.remark || '-' }}</el-descriptions-item>
        </el-descriptions>

        <!-- 物品图片 -->
        <div v-if="detailData.itemImages" class="item-images">
          <h4>物品图片</h4>
          <div class="image-gallery">
            <el-image
              v-for="(image, index) in getImageList(detailData.itemImages)"
              :key="index"
              :src="image"
              :preview-src-list="getImageList(detailData.itemImages)"
              :initial-index="index"
              fit="cover"
              class="image-item"
              lazy
            >
              <template #error>
                <div class="image-error">
                  <el-icon><Picture /></el-icon>
                  <span>加载失败</span>
                </div>
              </template>
              <template #placeholder>
                <div class="image-loading">
                  <el-icon><Loading /></el-icon>
                  <span>加载中...</span>
                </div>
              </template>
            </el-image>
          </div>
          <div class="image-tips">
            <el-icon><InfoFilled /></el-icon>
            <span>点击图片可查看大图，支持切换浏览</span>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onBeforeUnmount, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, Refresh, User, Download, Picture, Loading, InfoFilled, ArrowDown } from '@element-plus/icons-vue'
import { orderApi } from '@/api/order'
import { userApi } from '@/api/user'

// 响应式数据
const loading = ref(false)
const assignLoading = ref(false)
const detailVisible = ref(false)
const assignVisible = ref(false)
const isBatchAssign = ref(false)
const currentAssignOrder = ref(null)

// 表格数据
const tableData = ref([])
const selectedRows = ref([])

// 用户数据
const userList = ref([])

// 分页数据
const pagination = reactive({
  page: 1,
  size: 10,
  total: 0
})

// 搜索表单
const searchForm = reactive({
  orderNo: '',
  contactName: '',
  contactPhone: '',
  province: '',
  city: '',
  itemCategory: '',
  orderStatus: null,
  appointmentTimeSlot: '',
  appointmentDateRange: []
})

// 详情数据
const detailData = reactive({})

// 指派表单数据
const assignForm = reactive({
  confirmer: null,
  assignRemark: ''
})

// 页面加载时初始化
onMounted(() => {
  loadData()
  loadUsers()
})

// 加载数据
const loadData = async () => {
  loading.value = true
  try {
    const queryParams = {
      ...searchForm,
      appointmentStartDate: searchForm.appointmentDateRange?.[0] || null,
      appointmentEndDate: searchForm.appointmentDateRange?.[1] || null,
      page: pagination.page,
      size: pagination.size
    }
    delete queryParams.appointmentDateRange

    const response = await orderApi.queryOrders(queryParams)
    if (response.success) {
      tableData.value = response.data.content || []
      pagination.total = response.data.totalElements || 0
    } else {
      ElMessage.error(response.msg || '加载数据失败')
    }
  } catch (error) {
    console.error('加载数据失败:', error)
    ElMessage.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

// 加载用户数据
const loadUsers = async () => {
  try {
    const response = await userApi.getAllUsersByType(2)
    if (response.success) {
      // 如果返回的是分页数据，取content字段，否则直接使用data
      userList.value = response.data.content || response.data || []
    }
  } catch (error) {
    console.error('加载用户数据失败:', error)
  }
}

// 表格选择变化
const handleSelectionChange = (selection) => {
  console.log("selection", selection)
  selectedRows.value = selection
}

// 搜索
const handleSearch = () => {
  pagination.page = 1
  loadData()
}

// 重置搜索
const handleReset = () => {
  Object.assign(searchForm, {
    orderNo: '',
    contactName: '',
    contactPhone: '',
    province: '',
    city: '',
    itemCategory: '',
    orderStatus: null,
    appointmentTimeSlot: '',
    appointmentDateRange: []
  })
  pagination.page = 1
  loadData()
}

// 分页大小变化
const handleSizeChange = (size) => {
  pagination.size = size
  pagination.page = 1
  loadData()
}

// 当前页变化
const handleCurrentChange = (page) => {
  pagination.page = page
  loadData()
}

// 编辑
const handleEdit = (row) => {
  ElMessage.info('编辑功能开发中...')
}

// 单个指派确认人
const handleAssign = (row) => {
  isBatchAssign.value = false
  currentAssignOrder.value = row
  resetAssignForm()
  assignVisible.value = true
}

// 批量指派确认人
const handleBatchAssign = () => {
  if (!selectedRows.value.length) {
    ElMessage.warning('请选择要指派跑腿员的订单')
    return
  }

  isBatchAssign.value = true
  currentAssignOrder.value = null
  resetAssignForm()
  assignVisible.value = true
}

// 查看详情
const handleView = (row) => {
  Object.assign(detailData, row)
  detailVisible.value = true
}

// 重置指派表单
const resetAssignForm = () => {
  Object.assign(assignForm, {
    confirmer: null,
    assignRemark: ''
  })
}

// 提交指派确认人
const handleAssignSubmit = async () => {
  if (!assignForm.confirmer) {
    ElMessage.warning('请选择跑腿员')
    return
  }

  try {
    assignLoading.value = true

    // 构建订单ID列表
    let orderIds
    if (isBatchAssign.value) {
      orderIds = selectedRows.value.map(row => row.id)
    } else {
      orderIds = [currentAssignOrder.value.id]
    }

    // 调用指派确认人接口
    const response = await orderApi.assignConfirmer({
      orderIds: orderIds,
      confirmer: assignForm.confirmer,
      assignRemark: assignForm.assignRemark
    })

    if (response.success) {
      ElMessage.success('指派跑腿员成功')
      assignVisible.value = false
      loadData()
    } else {
      ElMessage.error(response.msg || '指派跑腿员失败')
    }
  } catch (error) {
    console.error('指派跑腿员失败:', error)
    ElMessage.error('指派跑腿员失败')
  } finally {
    assignLoading.value = false
  }
}



// 导出订单
const exportOrders = () => {
  ElMessage.info('导出功能开发中...')
}

// 获取状态类型
const getStatusType = (status) => {
  const statusMap = {
    1: 'warning',  // 待处理
    2: 'primary',  // 已确认
    3: 'success',  // 已完成
    4: 'danger'    // 已取消
  }
  return statusMap[status] || 'info'
}

const statusMap = {
  1: '待接单',
  2: '待上门',
  3: '待支付',
  4: '交易完成',
  5: "已关闭"
}
// 获取状态文本
const getStatusText = (status) => {
  return statusMap[status] || '未知'
}

// 获取图片列表
const getImageList = (images) => {
  if (!images) return []
  return images.split(',').filter(img => img.trim())
}

// 格式化日期时间
const formatDateTime = (dateTime) => {
  if (!dateTime) return ''
  return new Date(dateTime).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// ============== 移动端适配 ==============
const isMobile = ref(false)
const updateIsMobile = () => {
  isMobile.value = window.innerWidth <= 768
}
onMounted(() => {
  updateIsMobile()
  window.addEventListener('resize', updateIsMobile)
})
onBeforeUnmount(() => {
  window.removeEventListener('resize', updateIsMobile)
})

// 移动端折叠菜单统一处理
const handleMobileAction = (row, cmd) => {
  switch (cmd) {
    case 'view':
      handleView(row)
      break
    case 'assign':
      handleAssign(row)
      break
  }
}
</script>

<style scoped>
.order-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 60px);
  overflow-y: auto;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  color: #303133;
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
}

.page-header p {
  color: #909399;
  margin: 0;
  font-size: 14px;
}

.search-card {
  margin-bottom: 20px;
}

.search-form {
  margin-bottom: 20px;
}

.action-buttons {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.table-card {
  background: white;
  border-radius: 8px;
  overflow: hidden;
}

.text-muted {
  color: #909399;
}

.assigned-user {
  color: #67c23a;
  font-weight: 500;
}

.assign-content {
  padding: 10px 0;
}

.batch-info {
  margin-bottom: 20px;
}

.single-info {
  margin-bottom: 20px;
}

.assign-form {
  margin-top: 20px;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
  padding: 20px 0;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.order-detail {
  padding: 10px 0;
}

.item-images {
  margin-top: 20px;
}

.item-images h4 {
  margin: 0 0 15px 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.image-gallery {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
  margin-top: 10px;
}

.image-item {
  width: 120px;
  height: 120px;
  border-radius: 8px;
  cursor: pointer;
  border: 2px solid #e4e7ed;
  transition: all 0.3s ease;
  overflow: hidden;
}

.image-item:hover {
  border-color: #409eff;
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
}

.image-error,
.image-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #909399;
  font-size: 12px;
  background-color: #f5f7fa;
}

.image-error .el-icon,
.image-loading .el-icon {
  font-size: 24px;
  margin-bottom: 8px;
}

.image-tips {
  display: flex;
  align-items: center;
  gap: 6px;
  margin-top: 12px;
  padding: 8px 12px;
  background-color: #f0f9ff;
  border: 1px solid #b3e5fc;
  border-radius: 6px;
  color: #0277bd;
  font-size: 12px;
}

.image-tips .el-icon {
  font-size: 14px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .order-container {
    padding: 10px;
  }

  .search-form {
    flex-direction: column;
  }

  .search-form .el-form-item {
    margin-right: 0;
    margin-bottom: 15px;
  }

  .action-buttons {
    justify-content: center;
  }

  .el-table {
    font-size: 12px;
  }

  .image-gallery {
    justify-content: center;
    gap: 8px;
  }

  .image-item {
    width: 80px;
    height: 80px;
  }

  .image-tips {
    font-size: 11px;
    padding: 6px 10px;
  }

  /* 表格在移动端更紧凑 */
  :deep(.el-table .el-table__cell) {
    padding: 8px 0;
  }
}

/* 表格样式优化 */
:deep(.el-table) {
  border-radius: 8px;
  overflow: hidden;
}

:deep(.el-table th) {
  background-color: #fafafa;
  color: #606266;
  font-weight: 600;
}

:deep(.el-table td) {
  border-bottom: 1px solid #ebeef5;
}

:deep(.el-table tr:hover > td) {
  background-color: #f5f7fa;
}

/* 标签样式 */
:deep(.el-tag) {
  border-radius: 4px;
  font-weight: 500;
}

/* 按钮样式 */
:deep(.el-button) {
  border-radius: 6px;
  font-weight: 500;
}

:deep(.el-button--small) {
  padding: 5px 12px;
  font-size: 12px;
}

/* 对话框样式 */
:deep(.el-dialog) {
  border-radius: 12px;
  overflow: hidden;
}

:deep(.el-dialog__header) {
  background-color: #fafafa;
  padding: 20px 24px 16px;
  border-bottom: 1px solid #ebeef5;
}

:deep(.el-dialog__body) {
  padding: 24px;
}

:deep(.el-dialog__footer) {
  padding: 16px 24px 24px;
  border-top: 1px solid #ebeef5;
}

/* 表单样式 */
:deep(.el-form-item__label) {
  font-weight: 500;
  color: #606266;
}

:deep(.el-input__wrapper) {
  border-radius: 6px;
}

:deep(.el-select .el-input__wrapper) {
  border-radius: 6px;
}

:deep(.el-date-editor.el-input) {
  border-radius: 6px;
}

:deep(.el-textarea__inner) {
  border-radius: 6px;
}

/* 卡片样式 */
:deep(.el-card) {
  border-radius: 12px;
  border: 1px solid #ebeef5;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

:deep(.el-card__body) {
  padding: 24px;
}

/* 分页样式 */
:deep(.el-pagination) {
  justify-content: center;
}

:deep(.el-pagination .el-pager li) {
  border-radius: 6px;
  margin: 0 2px;
}

:deep(.el-pagination .btn-prev),
:deep(.el-pagination .btn-next) {
  border-radius: 6px;
}

/* 描述列表样式 */
:deep(.el-descriptions) {
  margin-top: 10px;
}

:deep(.el-descriptions__label) {
  font-weight: 600;
  color: #606266;
}

:deep(.el-descriptions__content) {
  color: #303133;
}

/* 图片预览样式 */
:deep(.el-image__inner) {
  border-radius: 6px;
}

:deep(.el-image__preview) {
  border-radius: 6px;
}
</style>
