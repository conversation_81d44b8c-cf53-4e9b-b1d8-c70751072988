<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>订单业务逻辑完整测试</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .workflow {
            display: flex;
            justify-content: space-between;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 10px;
            color: white;
        }
        .workflow-step {
            text-align: center;
            flex: 1;
            position: relative;
        }
        .workflow-step:not(:last-child)::after {
            content: '→';
            position: absolute;
            right: -20px;
            top: 50%;
            transform: translateY(-50%);
            font-size: 24px;
            font-weight: bold;
        }
        .api-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background-color: #fafafa;
        }
        .api-title {
            font-size: 18px;
            font-weight: bold;
            color: #409eff;
            margin-bottom: 15px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        input, textarea, select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            box-sizing: border-box;
        }
        .form-row {
            display: flex;
            gap: 15px;
        }
        .form-row .form-group {
            flex: 1;
        }
        button {
            background-color: #409eff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        button:hover {
            background-color: #66b1ff;
        }
        button.success {
            background-color: #67c23a;
        }
        button.success:hover {
            background-color: #85ce61;
        }
        button.warning {
            background-color: #e6a23c;
        }
        button.warning:hover {
            background-color: #ebb563;
        }
        .result {
            margin-top: 15px;
            padding: 15px;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
        .success {
            background-color: #f0f9ff;
            border: 1px solid #b3e5fc;
            color: #0277bd;
        }
        .error {
            background-color: #fff3e0;
            border: 1px solid #ffcc02;
            color: #f57c00;
        }
        .info {
            background-color: #e8f5e8;
            border: 1px solid #4caf50;
            color: #2e7d32;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-pending { background-color: #f56c6c; }
        .status-assigned { background-color: #e6a23c; }
        .status-confirmed { background-color: #67c23a; }
        .status-completed { background-color: #409eff; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔄 订单业务逻辑完整测试</h1>
        
        <!-- 业务流程图 -->
        <div class="workflow">
            <div class="workflow-step">
                <h3>1. 用户发起订单</h3>
                <p>创建回收订单</p>
            </div>
            <div class="workflow-step">
                <h3>2. 管理员分配</h3>
                <p>分配确认人员</p>
            </div>
            <div class="workflow-step">
                <h3>3. 确认人员确认</h3>
                <p>确认重量和金额</p>
            </div>
            <div class="workflow-step">
                <h3>4. 订单完成</h3>
                <p>交易完成</p>
            </div>
        </div>

        <!-- 步骤1: 创建测试订单 -->
        <div class="api-section">
            <div class="api-title">步骤1: 创建测试订单</div>
            <p>模拟用户发起回收订单</p>
            <button onclick="createTestOrder()" class="success">创建测试订单</button>
            <div id="createResult" class="result"></div>
        </div>

        <!-- 步骤2: 获取用户列表 -->
        <div class="api-section">
            <div class="api-title">步骤2: 获取用户列表</div>
            <p>获取可分配的确认人员列表</p>
            <button onclick="getAllUsers()">获取用户列表</button>
            <div id="usersResult" class="result"></div>
        </div>

        <!-- 步骤3: 分配确认人员 -->
        <div class="api-section">
            <div class="api-title">步骤3: 分配确认人员</div>
            <p>管理员为订单分配确认人员</p>
            <div class="form-row">
                <div class="form-group">
                    <label>订单ID列表 (逗号分隔):</label>
                    <input type="text" id="assignOrderIds" placeholder="如: 1,2,3">
                </div>
                <div class="form-group">
                    <label>确认人员ID:</label>
                    <input type="number" id="assignConfirmerId" placeholder="输入确认人员ID">
                </div>
            </div>
            <div class="form-group">
                <label>分配备注:</label>
                <textarea id="assignRemark" placeholder="输入分配备注（可选）">请及时确认订单信息，注意核实重量</textarea>
            </div>
            <button onclick="assignConfirmer()" class="warning">分配确认人员</button>
            <div id="assignResult" class="result"></div>
        </div>

        <!-- 步骤4: 查询待确认订单 -->
        <div class="api-section">
            <div class="api-title">步骤4: 查询待确认订单</div>
            <p>确认人员查看分配给自己的待确认订单</p>
            <div class="form-group">
                <label>确认人员ID:</label>
                <input type="number" id="queryConfirmerId" placeholder="输入确认人员ID">
            </div>
            <button onclick="getPendingConfirmOrders()">查询待确认订单</button>
            <div id="queryResult" class="result"></div>
        </div>

        <!-- 步骤5: 确认订单 -->
        <div class="api-section">
            <div class="api-title">步骤5: 确认订单</div>
            <p>确认人员确认订单的实际重量和交易金额</p>
            <div class="form-row">
                <div class="form-group">
                    <label>订单ID:</label>
                    <input type="number" id="confirmOrderId" placeholder="输入订单ID">
                </div>
                <div class="form-group">
                    <label>确认重量(kg):</label>
                    <input type="number" id="confirmWeight" placeholder="输入确认重量" step="0.01" value="12.50">
                </div>
            </div>
            <div class="form-row">
                <div class="form-group">
                    <label>交易金额(元):</label>
                    <input type="number" id="transactionAmount" placeholder="输入交易金额" step="0.01" value="125.00">
                </div>
            </div>
            <div class="form-group">
                <label>确认备注:</label>
                <textarea id="confirmRemark" placeholder="输入确认备注（可选）">实际重量与预估重量基本一致，交易完成</textarea>
            </div>
            <button onclick="confirmOrder()" class="success">确认订单</button>
            <div id="confirmResult" class="result"></div>
        </div>

        <!-- 步骤6: 验证完整流程 -->
        <div class="api-section">
            <div class="api-title">步骤6: 验证完整流程</div>
            <p>查看订单详情，验证整个业务流程</p>
            <div class="form-group">
                <label>订单ID:</label>
                <input type="number" id="verifyOrderId" placeholder="输入订单ID">
            </div>
            <button onclick="verifyOrder()">查看订单详情</button>
            <div id="verifyResult" class="result"></div>
        </div>

        <!-- 一键测试完整流程 -->
        <div class="api-section">
            <div class="api-title">🚀 一键测试完整流程</div>
            <p>自动执行完整的业务流程测试</p>
            <button onclick="runFullWorkflowTest()" style="background-color: #f56c6c; font-size: 16px; padding: 15px 30px;">
                🎯 执行完整流程测试
            </button>
            <div id="fullTestResult" class="result"></div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8081';
        let testOrderId = null;
        let testConfirmerId = null;

        // 通用请求函数
        async function apiRequest(url, options = {}) {
            try {
                const response = await fetch(API_BASE + url, {
                    headers: {
                        'Content-Type': 'application/json',
                        ...options.headers
                    },
                    ...options
                });
                
                const result = await response.json();
                return result;
            } catch (error) {
                return { success: false, msg: '请求失败: ' + error.message };
            }
        }

        // 显示结果
        function showResult(elementId, result, type = 'info') {
            const element = document.getElementById(elementId);
            element.textContent = JSON.stringify(result, null, 2);
            element.className = `result ${type}`;
        }

        // 1. 创建测试订单
        async function createTestOrder() {
            const testOrder = {
                contactName: '测试用户' + Math.floor(Math.random() * 1000),
                contactPhone: '138' + Math.floor(Math.random() * 100000000).toString().padStart(8, '0'),
                province: '广东省',
                city: '深圳市',
                district: '南山区',
                detailedAddress: '科技园南区深圳湾科技生态园' + Math.floor(Math.random() * 100) + '栋',
                itemCategory: ['废纸', '废塑料', '废金属', '电子废料'][Math.floor(Math.random() * 4)],
                estimatedWeight: Math.floor(Math.random() * 50) + 10,
                appointmentDate: '2024-12-25',
                appointmentTimeSlot: ['上午', '下午', '晚上'][Math.floor(Math.random() * 3)],
                remark: '测试订单，用于完整业务流程测试'
            };

            const result = await apiRequest('/api/recycle-orders', {
                method: 'POST',
                body: JSON.stringify(testOrder)
            });
            
            showResult('createResult', result, result.success ? 'success' : 'error');
            
            if (result.success) {
                testOrderId = result.data.id;
                // 自动填充订单ID到后续表单
                document.getElementById('assignOrderIds').value = testOrderId;
                document.getElementById('confirmOrderId').value = testOrderId;
                document.getElementById('verifyOrderId').value = testOrderId;
            }
            
            return result;
        }

        // 2. 获取所有用户
        async function getAllUsers() {
            const result = await apiRequest('/api/users?page=0&size=0');
            showResult('usersResult', result, result.success ? 'success' : 'error');
            
            if (result.success && result.data) {
                const users = result.data.content || result.data;
                if (users.length > 0) {
                    testConfirmerId = users[0].id;
                    document.getElementById('assignConfirmerId').value = testConfirmerId;
                    document.getElementById('queryConfirmerId').value = testConfirmerId;
                }
            }
            
            return result;
        }

        // 3. 分配确认人员
        async function assignConfirmer() {
            const orderIdsStr = document.getElementById('assignOrderIds').value;
            const confirmerId = document.getElementById('assignConfirmerId').value;
            const assignRemark = document.getElementById('assignRemark').value;
            
            if (!orderIdsStr || !confirmerId) {
                showResult('assignResult', { success: false, msg: '请输入订单ID列表和确认人员ID' }, 'error');
                return { success: false };
            }

            const orderIds = orderIdsStr.split(',').map(id => parseInt(id.trim())).filter(id => !isNaN(id));
            
            if (orderIds.length === 0) {
                showResult('assignResult', { success: false, msg: '订单ID格式不正确' }, 'error');
                return { success: false };
            }

            const result = await apiRequest('/api/recycle-orders/assign-confirmer', {
                method: 'POST',
                body: JSON.stringify({
                    orderIds: orderIds,
                    confirmerId: parseInt(confirmerId),
                    assignRemark: assignRemark
                })
            });
            
            showResult('assignResult', result, result.success ? 'success' : 'error');
            return result;
        }

        // 4. 查询待确认订单
        async function getPendingConfirmOrders() {
            const confirmerId = document.getElementById('queryConfirmerId').value;
            
            if (!confirmerId) {
                showResult('queryResult', { success: false, msg: '请输入确认人员ID' }, 'error');
                return { success: false };
            }

            const result = await apiRequest(`/api/recycle-orders/pending-confirm/${confirmerId}`);
            showResult('queryResult', result, result.success ? 'success' : 'error');
            return result;
        }

        // 5. 确认订单
        async function confirmOrder() {
            const orderId = document.getElementById('confirmOrderId').value;
            const confirmWeight = document.getElementById('confirmWeight').value;
            const transactionAmount = document.getElementById('transactionAmount').value;
            const confirmRemark = document.getElementById('confirmRemark').value;
            
            if (!orderId || !confirmWeight || !transactionAmount) {
                showResult('confirmResult', { success: false, msg: '请输入订单ID、确认重量和交易金额' }, 'error');
                return { success: false };
            }

            const result = await apiRequest('/api/recycle-orders/confirm', {
                method: 'POST',
                body: JSON.stringify({
                    orderId: parseInt(orderId),
                    confirmWeight: parseFloat(confirmWeight),
                    transactionAmount: parseFloat(transactionAmount),
                    confirmRemark: confirmRemark
                })
            });
            
            showResult('confirmResult', result, result.success ? 'success' : 'error');
            return result;
        }

        // 6. 验证订单
        async function verifyOrder() {
            const orderId = document.getElementById('verifyOrderId').value;
            
            if (!orderId) {
                showResult('verifyResult', { success: false, msg: '请输入订单ID' }, 'error');
                return { success: false };
            }

            const result = await apiRequest(`/api/recycle-orders/${orderId}`);
            showResult('verifyResult', result, result.success ? 'success' : 'error');
            return result;
        }

        // 完整流程测试
        async function runFullWorkflowTest() {
            const resultDiv = document.getElementById('fullTestResult');
            resultDiv.textContent = '开始执行完整流程测试...\n';
            resultDiv.className = 'result info';

            try {
                // 步骤1: 创建订单
                resultDiv.textContent += '\n步骤1: 创建测试订单...\n';
                const createResult = await createTestOrder();
                if (!createResult.success) {
                    throw new Error('创建订单失败: ' + createResult.msg);
                }
                resultDiv.textContent += `✅ 订单创建成功，订单ID: ${testOrderId}\n`;

                // 步骤2: 获取用户
                resultDiv.textContent += '\n步骤2: 获取用户列表...\n';
                const usersResult = await getAllUsers();
                if (!usersResult.success) {
                    throw new Error('获取用户失败: ' + usersResult.msg);
                }
                resultDiv.textContent += `✅ 用户列表获取成功，确认人员ID: ${testConfirmerId}\n`;

                // 等待1秒
                await new Promise(resolve => setTimeout(resolve, 1000));

                // 步骤3: 分配确认人员
                resultDiv.textContent += '\n步骤3: 分配确认人员...\n';
                const assignResult = await assignConfirmer();
                if (!assignResult.success) {
                    throw new Error('分配确认人员失败: ' + assignResult.msg);
                }
                resultDiv.textContent += '✅ 确认人员分配成功\n';

                // 等待1秒
                await new Promise(resolve => setTimeout(resolve, 1000));

                // 步骤4: 查询待确认订单
                resultDiv.textContent += '\n步骤4: 查询待确认订单...\n';
                const queryResult = await getPendingConfirmOrders();
                if (!queryResult.success) {
                    throw new Error('查询待确认订单失败: ' + queryResult.msg);
                }
                resultDiv.textContent += `✅ 待确认订单查询成功，找到 ${queryResult.data.length} 个订单\n`;

                // 等待1秒
                await new Promise(resolve => setTimeout(resolve, 1000));

                // 步骤5: 确认订单
                resultDiv.textContent += '\n步骤5: 确认订单...\n';
                const confirmResult = await confirmOrder();
                if (!confirmResult.success) {
                    throw new Error('确认订单失败: ' + confirmResult.msg);
                }
                resultDiv.textContent += '✅ 订单确认成功\n';

                // 等待1秒
                await new Promise(resolve => setTimeout(resolve, 1000));

                // 步骤6: 验证最终结果
                resultDiv.textContent += '\n步骤6: 验证最终结果...\n';
                const verifyResult = await verifyOrder();
                if (!verifyResult.success) {
                    throw new Error('验证订单失败: ' + verifyResult.msg);
                }
                resultDiv.textContent += '✅ 订单验证成功\n';

                resultDiv.textContent += '\n🎉 完整流程测试成功！\n';
                resultDiv.textContent += `\n最终订单状态: ${verifyResult.data.orderStatusDesc}\n`;
                resultDiv.textContent += `确认人员: ${verifyResult.data.confirmerName || '未设置'}\n`;
                resultDiv.textContent += `确认重量: ${verifyResult.data.confirmWeight || '未确认'} kg\n`;
                resultDiv.textContent += `交易金额: ¥${verifyResult.data.transactionAmount || '未确认'}\n`;
                resultDiv.textContent += `确认时间: ${verifyResult.data.confirmDate || '未确认'}\n`;

                resultDiv.className = 'result success';

            } catch (error) {
                resultDiv.textContent += `\n❌ 测试失败: ${error.message}\n`;
                resultDiv.className = 'result error';
            }
        }
    </script>
</body>
</html>
