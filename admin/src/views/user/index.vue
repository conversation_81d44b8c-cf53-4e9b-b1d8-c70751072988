<template>
  <div class="user-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>用户管理</h2>
      <p>管理系统用户信息、角色权限和账户状态</p>
    </div>

    <!-- 搜索和操作区域 -->
    <el-card class="search-card" shadow="never">
      <!-- 搜索表单 -->
      <el-form :model="searchForm" class="search-form" :inline="true">
        <el-form-item label="用户名">
          <el-input
            v-model="searchForm.userNo"
            placeholder="请输入用户名"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="姓名">
          <el-input
            v-model="searchForm.nickName"
            placeholder="请输入姓名"
            clearable
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="显示会员">
          <el-select v-model="searchForm.showVip" placeholder="请选择状态" clearable style="width: 120px">
            <el-option label="显示" :value="1" />
            <el-option label="不显示" :value="0" />
          </el-select>
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="searchForm.state" placeholder="请选择状态" clearable style="width: 120px">
            <el-option label="启用" value="1" />
            <el-option label="禁用" value="0" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>

      <!-- 操作按钮 -->
      <div class="action-buttons">
        <el-button type="primary" @click="handleAdd">
          <el-icon><Plus /></el-icon>
          新增用户
        </el-button>
      </div>
    </el-card>

    <!-- 表格区域 -->
    <el-card class="table-card" shadow="never">
      <el-table
        :data="users"
        v-loading="loading"
        stripe
        border
        style="width: 100%"
      >
        <el-table-column prop="id" label="ID" width="80" align="center" />
        <el-table-column prop="userNo" label="用户名" show-overflow-tooltip />
        <el-table-column prop="nickName" label="姓名" show-overflow-tooltip />
        <el-table-column prop="roleName" label="用户类型" :width="isMobile ? 120 : 200">
          <template #default="{ row }">
            {{userTypes[(row.userType||1) - 1]}}
          </template>
        </el-table-column>
        <el-table-column prop="state" label="状态" width="100" align="center">
          <template #default="{ row }">
            <el-tag :type="row.state === '1' ? 'success' : 'danger'" size="small">
              {{ row.state === '1' ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="isLocked" label="锁定状态" width="100" align="center">
          <template #default="{ row }">
            <el-tag :type="row.isLocked === '1' ? 'warning' : 'success'" size="small">
              {{ row.isLocked === '1' ? '已锁定' : '正常' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createDate" label="创建时间" width="180" align="center">
          <template #default="{ row }">
            {{ formatDateTime(row.createDate) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" :width="isMobile ? 120 : 280" fixed="right" align="center">
          <template #default="{ row }">
            <template v-if="!isMobile">
              <el-button type="primary" size="small" @click="handleView(row)">查看</el-button>
              <el-button type="success" size="small" @click="handleEdit(row)">编辑</el-button>
              &nbsp;
              <el-dropdown @command="(command) => handleAction(row, command)">
                <el-button type="warning" size="small">
                  更多<el-icon class="el-icon--right"><ArrowDown /></el-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item command="resetPwd">重置密码</el-dropdown-item>
                    <el-dropdown-item command="lock" v-if="row.isLocked !== '1'">锁定账户</el-dropdown-item>
                    <el-dropdown-item command="unlock" v-if="row.isLocked === '1'">解锁账户</el-dropdown-item>
                    <el-dropdown-item command="delete" divided>删除用户</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </template>
            <template v-else>
              <el-dropdown trigger="click" @command="(cmd) => handleMobileAction(row, cmd)">
                <el-button type="primary" size="small">
                  操作<el-icon class="el-icon--right"><ArrowDown /></el-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item command="view">查看</el-dropdown-item>
                    <el-dropdown-item command="edit">编辑</el-dropdown-item>
                    <el-dropdown-item command="resetPwd">重置密码</el-dropdown-item>
                    <el-dropdown-item command="lock" v-if="row.isLocked !== '1'">锁定账户</el-dropdown-item>
                    <el-dropdown-item command="unlock" v-if="row.isLocked === '1'">解锁账户</el-dropdown-item>
                    <el-dropdown-item command="delete" divided>删除用户</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </template>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          background
          :layout="isMobile ? 'total, prev, next' : 'total, sizes, prev, pager, next, jumper'"
          :total="total"
          :page-size="pageSize"
          :current-page="page"
          @current-change="handlePageChange"
          @size-change="handleSizeChange"
          :page-sizes="[10, 20, 50, 100]"
        />
      </div>
    </el-card>

    <!-- 用户详情对话框 -->
    <el-dialog
      title="用户详情"
      v-model="detailVisible"
      :width="isMobile ? '96%' : '800px'"
      :close-on-click-modal="false"
    >
      <el-descriptions :column="isMobile ? 1 : 2" border size="default">
        <el-descriptions-item label="用户ID">{{ detailData.id }}</el-descriptions-item>
        <el-descriptions-item label="用户名">{{ detailData.userNo }}</el-descriptions-item>
        <el-descriptions-item label="姓名">{{ detailData.nickName }}</el-descriptions-item>
        <el-descriptions-item label="状态">
          <el-tag :type="detailData.state === '1' ? 'success' : 'danger'" size="small">
            {{ detailData.state === '1' ? '启用' : '禁用' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="锁定状态">
          <el-tag :type="detailData.isLocked === '1' ? 'warning' : 'success'" size="small">
            {{ detailData.isLocked === '1' ? '已锁定' : '正常' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="用户类型" :span="2">
          {{userTypes[(detailData.userType||1) - 1]}}
        </el-descriptions-item>
        <el-descriptions-item label="创建时间">{{ formatDateTime(detailData.createDate) }}</el-descriptions-item>
        <el-descriptions-item label="更新时间">{{ formatDateTime(detailData.updateDate) }}</el-descriptions-item>
      </el-descriptions>
    </el-dialog>

    <!-- 新增用户对话框 -->
    <el-dialog
      title="新增用户"
      v-model="addDialogVisible"
      :width="isMobile ? '96%' : '600px'"
      :close-on-click-modal="false"
    >
      <el-form :model="addForm" :rules="addFormRules" ref="addFormRef" label-width="100px">
        <el-row :gutter="20">
          <el-col :span="isMobile ? 24 : 12">
            <el-form-item label="用户名" prop="userNo">
              <el-input v-model="addForm.userNo" placeholder="请输入用户名" />
            </el-form-item>
          </el-col>
          <el-col :span="isMobile ? 24 : 12">
            <el-form-item label="姓名" prop="nickName">
              <el-input v-model="addForm.nickName" placeholder="请输入姓名" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="isMobile ? 24 : 12">
            <el-form-item label="密码" prop="passwd">
              <el-input v-model="addForm.passwd" type="password" placeholder="请输入密码" show-password />
            </el-form-item>
          </el-col>
          <el-col :span="isMobile ? 24 : 12">
            <el-form-item label="状态" prop="state">
              <el-select v-model="addForm.state" placeholder="请选择状态" style="width: 100%">
                <el-option label="启用" value="1" />
                <el-option label="禁用" value="0" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="用户类型" prop="groupId">
          <el-select v-model="addForm.userType" filterable placeholder="请选择用户类型" style="width: 100%">
            <el-option v-for="(item, index) in userTypes" :key="index + 1" :label="item" :value="index + 1" v-show="index !== 3" />
          </el-select>
        </el-form-item>

        <el-form-item label="佣金" prop="brokerageExpenses" v-if="isAddPromoter">
          <el-input v-model="addForm.brokerageExpenses" placeholder="请输入0-100之间的数字">
            <template #append>元/单</template>
          </el-input>
        </el-form-item>

<!--        <el-form-item label="角色" prop="roleIds">-->
<!--          <el-select v-model="addForm.roleIds" multiple filterable placeholder="请选择角色" style="width: 100%">-->
<!--            <el-option v-for="item in roleOptions" :key="item.value" :label="item.label" :value="item.value" />-->
<!--          </el-select>-->
<!--        </el-form-item>-->
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="addDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitAdd" :loading="submitLoading">
            确定
          </el-button>
        </span>
      </template>
    </el-dialog>
    <!-- 编辑用户对话框 -->
    <el-dialog
      title="编辑用户"
      v-model="editDialogVisible"
      :width="isMobile ? '96%' : '600px'"
      :close-on-click-modal="false"
    >
      <el-form :model="editForm" :rules="editFormRules" ref="editFormRef" label-width="100px">
        <el-row :gutter="20">
          <el-col :span="isMobile ? 24 : 12">
            <el-form-item label="用户名" prop="userNo">
              <el-input v-model="editForm.userNo" placeholder="请输入用户名" />
            </el-form-item>
          </el-col>
          <el-col :span="isMobile ? 24 : 12">
            <el-form-item label="姓名" prop="nickName">
              <el-input v-model="editForm.nickName" placeholder="请输入姓名" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="isMobile ? 24 : 12">
            <el-form-item label="密码" prop="passwd">
              <el-input
                v-model="editForm.passwd"
                type="password"
                placeholder="如需修改请填写"
                show-password
              />
            </el-form-item>
          </el-col>
          <el-col :span="isMobile ? 24 : 12">
            <el-form-item label="状态" prop="state">
              <el-select v-model="editForm.state" placeholder="请选择状态" style="width: 100%">
                <el-option label="启用" value="1" />
                <el-option label="禁用" value="0" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="用户类型" prop="groupId">
          <el-select v-model="editForm.userType" filterable placeholder="请选择用户类型" style="width: 100%">
            <el-option v-for="(item, index) in userTypes" :key="index + 1" :label="item" :value="index + 1" v-show="index != 3" />
          </el-select>
        </el-form-item>

        <el-form-item label="佣金" prop="brokerageExpenses" v-if="isEditPromoter">
          <el-input v-model="editForm.brokerageExpenses" placeholder="请输入0-100之间的数字">
            <template #append>元/单</template>
          </el-input>
        </el-form-item>

<!--        <el-form-item label="角色" prop="roleIds">-->
<!--          <el-select v-model="editForm.roleIds" multiple filterable placeholder="请选择角色" style="width: 100%">-->
<!--            <el-option v-for="item in roleOptions" :key="item.value" :label="item.label" :value="item.value" />-->
<!--          </el-select>-->
<!--        </el-form-item>-->
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="editDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitEdit" :loading="submitLoading">
            确定
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 重置密码对话框 -->
    <el-dialog
      title="重置密码"
      v-model="resetPwdDialogVisible"
      :width="isMobile ? '92%' : '500px'"
      :close-on-click-modal="false"
    >
      <el-form :model="resetPwdForm" :rules="resetPwdFormRules" ref="resetPwdFormRef" label-width="100px">
        <el-form-item label="用户信息">
          <div class="user-info">
            <span>{{ resetPwdForm.nickName }} ({{ resetPwdForm.userNo }})</span>
          </div>
        </el-form-item>
        <el-form-item label="新密码" prop="newPassword">
          <el-input
            v-model="resetPwdForm.newPassword"
            type="password"
            placeholder="请输入新密码"
            show-password
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="resetPwdDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitResetPwd" :loading="submitLoading">
            确定
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted, onBeforeUnmount, reactive, computed, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Search,
  Refresh,
  Plus,
  Download,
  ArrowDown
} from '@element-plus/icons-vue'
import { userApi } from '@/api/user.js'
import { roleApi } from '@/api/role.js'

// 响应式数据
const loading = ref(false)
const submitLoading = ref(false)
const detailVisible = ref(false)

// 表格数据
const users = ref([])
const total = ref(0)
const page = ref(1)
const pageSize = ref(10)

// 搜索表单
const searchForm = reactive({
  userNo: '',
  nickName: '',
  state: '',
  showVip: 0
})

// 详情数据
const detailData = reactive({})

// 新增表单
const addDialogVisible = ref(false)
const addForm = reactive({
  nickName: '',
  userNo: '',
  passwd: '',
  userType: '',
  state: '1',
  brokerageExpenses: ''
})
const addFormRules = {
  nickName: [
    { required: true, message: '请输入姓名', trigger: 'blur' },
    { max: 50, message: '姓名长度不能超过50个字符', trigger: 'blur' }
  ],
  userNo: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 20, message: '用户名长度在3到20个字符', trigger: 'blur' },
    { pattern: /^[a-zA-Z0-9_]+$/, message: '用户名只能包含字母、数字和下划线', trigger: 'blur' }
  ],
  passwd: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 20, message: '密码长度在6到20个字符', trigger: 'blur' }
  ],
  state: [{ required: true, message: '请选择状态', trigger: 'change' }],
  brokerageExpenses: [
    { validator: (rule, value, callback) => {
        // 仅当用户类型为推广员(3)时需要校验
        if (String(addForm.userType) === '3') {
          if (value === '' || value === null || value === undefined) {
            return callback(new Error('请输入佣金比例'))
          }
          if (Number.isNaN(Number(value))) {
            return callback(new Error('佣金必须为数字'))
          }
          const num = Number(value)
          if (num < 0 || num > 100) {
            return callback(new Error('佣金应在0-100之间'))
          }
        }
        return callback()
      }, trigger: 'blur' }
  ]
}
const addFormRef = ref()

// 编辑表单
const editDialogVisible = ref(false)
const editForm = reactive({
  id: '',
  nickName: '',
  userNo: '',
  passwd: '',
  userType: '',
  state: '1',
  brokerageExpenses: ''
})
const editFormRules = {
  nickName: [
    { required: true, message: '请输入姓名', trigger: 'blur' },
    { max: 50, message: '姓名长度不能超过50个字符', trigger: 'blur' }
  ],
  userNo: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 50, message: '用户名长度在3到20个字符', trigger: 'blur' },
    { pattern: /^[a-zA-Z0-9_]+$/, message: '用户名只能包含字母、数字和下划线', trigger: 'blur' }
  ],
  state: [{ required: true, message: '请选择状态', trigger: 'change' }],
  brokerageExpenses: [
    { validator: (rule, value, callback) => {
        if (String(editForm.userType) === '3') {
          if (value === '' || value === null || value === undefined) {
            return callback(new Error('请输入佣金比例'))
          }
          if (Number.isNaN(Number(value))) {
            return callback(new Error('佣金必须为数字'))
          }
          const num = Number(value)
          if (num < 0 || num > 100) {
            return callback(new Error('佣金应在0-100之间'))
          }
        }
        return callback()
      }, trigger: 'blur' }
  ]
}
const editFormRef = ref()

// 重置密码表单
const resetPwdDialogVisible = ref(false)
const resetPwdForm = reactive({
  id: '',
  nickName: '',
  userNo: '',
  newPassword: ''
})
const resetPwdFormRules = {
  newPassword: [
    { required: true, message: '请输入新密码', trigger: 'blur' },
    { min: 6, max: 20, message: '密码长度在6到20个字符', trigger: 'blur' }
  ]
}
const resetPwdFormRef = ref()

// 角色选项
const roleOptions = ref([])
// 角色选项
const userTypes = ['管理员','跑腿员','推广员','会员'];

// 仅推广员可设置佣金
const isAddPromoter = computed(() => String(addForm.userType) === '3')
const isEditPromoter = computed(() => String(editForm.userType) === '3')

// 切换为非推广员时清空佣金
watch(() => addForm.userType, (v) => {
  if (String(v) !== '3') {
    addForm.brokerageExpenses = ''
  }
})
watch(() => editForm.userType, (v) => {
  if (String(v) !== '3') {
    editForm.brokerageExpenses = ''
  }
})

// 页面加载
onMounted(() => {
  fetchUsers()
  // fetchRoles()
})

// 获取用户列表
const fetchUsers = async () => {
  loading.value = true
  try {
    const params = {
      page: page.value - 1,
      size: pageSize.value,
      ...searchForm
    }
    const res = await userApi.getUsers(params)
    if (res && res.data) {
      users.value = res.data.content || []
      total.value = res.data.totalElements || 0
    } else {
      users.value = []
      total.value = 0
    }
  } catch (e) {
    ElMessage.error('获取用户列表失败')
  } finally {
    loading.value = false
  }
}

// 获取角色列表
const fetchRoles = async () => {
  try {
    const res = await roleApi.getRoles()
    if (res && res.data) {
      roleOptions.value = res.data.map(role => ({
        label: role.roleName,
        value: role.id
      }))
    }
  } catch (e) {
    console.error('获取角色列表失败:', e)
  }
}

// 搜索
const handleSearch = () => {
  page.value = 1
  fetchUsers()
}

// 重置搜索
const handleReset = () => {
  Object.assign(searchForm, {
    userNo: '',
    nickName: '',
    state: '',
    showVip: 0
  })
  page.value = 1
  fetchUsers()
}

// 查看详情
const handleView = (row) => {
  Object.assign(detailData, row)
  detailVisible.value = true
}

// 导出用户
const exportUsers = () => {
  ElMessage.info('导出功能开发中...')
}

// 格式化时间
const formatDateTime = (dateTime) => {
  if (!dateTime) return '-'
  return new Date(dateTime).toLocaleString('zh-CN')
}

// 分页处理
const handlePageChange = (newPage) => {
  page.value = newPage
  fetchUsers()
}

const handleSizeChange = (newSize) => {
  pageSize.value = newSize
  page.value = 1
  fetchUsers()
}

// 新增用户
const handleAdd = () => {
  Object.assign(addForm, {
    nickName: '',
    userNo: '',
    passwd: '',
    userType: '',
    state: '1',
    roleIds: []
  })
  addDialogVisible.value = true
}

const submitAdd = async () => {
  if (!addFormRef.value) return

  try {
    await addFormRef.value.validate()
    submitLoading.value = true

    const res = await userApi.createUser(addForm)
    if (res && res.success) {
      ElMessage.success('新增用户成功')
      addDialogVisible.value = false
      fetchUsers()
    } else {
      ElMessage.error(res?.msg || '新增用户失败')
    }
  } catch (e) {
    console.error('新增用户失败:', e)
    ElMessage.error('新增用户失败')
  } finally {
    submitLoading.value = false
  }
}

// 编辑用户
const handleEdit = (row) => {
  Object.assign(editForm, row)
  editDialogVisible.value = true
}

const submitEdit = async () => {
  if (!editFormRef.value) return

  try {
    await editFormRef.value.validate()
    submitLoading.value = true

    const res = await userApi.updateUser(editForm.id, editForm)
    if (res && res.success) {
      ElMessage.success('更新用户成功')
      editDialogVisible.value = false
      fetchUsers()
    } else {
      ElMessage.error(res?.msg || '更新用户失败')
    }
  } catch (e) {
    console.error('更新用户失败:', e)
    ElMessage.error('更新用户失败')
  } finally {
    submitLoading.value = false
  }
}

// 统一操作处理
const handleAction = async (row, command) => {
  switch (command) {
    case 'resetPwd':
      handleResetPwd(row)
      break
    case 'lock':
      await handleLock(row)
      break
    case 'unlock':
      await handleUnlock(row)
      break
    case 'delete':
      await handleDelete(row)
      break
  }
}

const handleResetPwd = (row) => {
  resetPwdForm.id = row.id
  resetPwdForm.nickName = row.nickName
  resetPwdForm.userNo = row.userNo
  resetPwdForm.newPassword = ''
  resetPwdDialogVisible.value = true
}

const submitResetPwd = async () => {
  if (!resetPwdFormRef.value) return

  try {
    await resetPwdFormRef.value.validate()
    submitLoading.value = true

    const res = await userApi.resetPassword(resetPwdForm.id, resetPwdForm.newPassword)
    if (res && res.success) {
      ElMessage.success('重置密码成功')
      resetPwdDialogVisible.value = false
    } else {
      ElMessage.error(res?.msg || '重置密码失败')
    }
  } catch (e) {
    console.error('重置密码失败:', e)
    ElMessage.error('重置密码失败')
  } finally {
    submitLoading.value = false
  }
}

const handleLock = async (row) => {
  try {
    await ElMessageBox.confirm(`确定要锁定用户"${row.nickName}"吗？`, '锁定确认', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    const res = await userApi.lockUser(row.id)
    if (res && res.success) {
      ElMessage.success('锁定用户成功')
      fetchUsers()
    } else {
      ElMessage.error(res?.msg || '锁定用户失败')
    }
  } catch (e) {
    if (e !== 'cancel') {
      console.error('锁定用户失败:', e)
      ElMessage.error('锁定用户失败')
    }
  }
}

const handleUnlock = async (row) => {
  try {
    await ElMessageBox.confirm(`确定要解锁用户"${row.nickName}"吗？`, '解锁确认', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'info'
    })

    const res = await userApi.unlockUser(row.id)
    if (res && res.success) {
      ElMessage.success('解锁用户成功')
      fetchUsers()
    } else {
      ElMessage.error(res?.msg || '解锁用户失败')
    }
  } catch (e) {
    if (e !== 'cancel') {
      console.error('解锁用户失败:', e)
      ElMessage.error('解锁用户失败')
    }
  }
}

const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(`确定要删除用户"${row.nickName}"吗？`, '删除确认', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    const res = await userApi.deleteUser(row.id)
    if (res && res.success) {
      ElMessage.success('删除用户成功')
      fetchUsers()
    } else {
      ElMessage.error(res?.msg || '删除用户失败')
    }
  } catch (e) {
    if (e !== 'cancel') {
      console.error('删除用户失败:', e)
      ElMessage.error('删除用户失败')
    }
  }
}

// ============== 移动端适配 ==============
const isMobile = ref(false)
const updateIsMobile = () => {
  isMobile.value = window.innerWidth <= 768
}
onMounted(() => {
  updateIsMobile()
  window.addEventListener('resize', updateIsMobile)
})
onBeforeUnmount(() => {
  window.removeEventListener('resize', updateIsMobile)
})

// 移动端折叠菜单统一处理
const handleMobileAction = (row, cmd) => {
  switch (cmd) {
    case 'view':
      handleView(row)
      break
    case 'edit':
      handleEdit(row)
      break
    default:
      handleAction(row, cmd)
  }
}
</script>

<style scoped>
.user-container {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 24px;
  font-weight: 600;
}

.page-header p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.search-card {
  margin-bottom: 20px;
}

.search-form {
  margin-bottom: 20px;
}

.action-buttons {
  display: flex;
  gap: 12px;
}

.table-card {
  margin-bottom: 20px;
}

/* 允许纵向滚动，防止移动端高度溢出 */
.user-container {
  overflow-y: auto;
}

.pagination-container {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
}

.text-muted {
  color: #909399;
}

.user-info {
  padding: 10px;
  background-color: #f5f7fa;
  border-radius: 4px;
  color: #606266;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .user-container {
    padding: 10px;
  }

  .search-form {
    display: block;
  }

  .search-form .el-form-item {
    margin-bottom: 15px;
  }

  .action-buttons {
    flex-direction: column;
  }

  .action-buttons .el-button {
    margin-bottom: 10px;
  }

  /* 表格在移动端更紧凑 */
  :deep(.el-table .el-table__cell) {
    padding: 8px 0;
  }
}

/* Element Plus 样式覆盖 */
:deep(.el-card__body) {
  padding: 20px;
}

:deep(.el-table .el-table__cell) {
  padding: 12px 0;
}

:deep(.el-descriptions__label) {
  font-weight: 600;
}
</style>
