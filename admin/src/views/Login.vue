<template>
  <div class="login-container">
    <!-- 背景装饰 -->
    <div class="login-background">
      <div class="bg-circle circle-1"></div>
      <div class="bg-circle circle-2"></div>
      <div class="bg-circle circle-3"></div>
    </div>

    <!-- 登录卡片 -->
    <div class="login-wrapper">
      <div class="login-card">
        <!-- 品牌头部 -->
        <div class="login-header">
          <div class="brand-logo">
            <el-image style="width: 50px; height: 50px" :src="url" fit="contain" />
          </div>
          <h1 class="brand-title">叮咚回收</h1>
          <p class="brand-subtitle">智能回收管理系统</p>
        </div>

                  <!-- 表单容器 -->
          <div class="form-container">
            <!-- 登录表单 -->
            <div class="login-form-wrapper">
              <h2 class="form-title">欢迎登录</h2>

              <el-form :model="loginForm" :rules="rules" ref="loginFormRef" class="login-form">
                <el-form-item prop="telephone">
                  <el-input
                    v-model="loginForm.telephone"
                    placeholder="请输入手机号"
                    size="large"
                    clearable
                    prefix-icon="Phone"
                    class="login-input"
                    maxlength="11"
                  />
                </el-form-item>

                <el-form-item prop="code">
                  <div class="code-input-group">
                    <el-input
                      v-model="loginForm.code"
                      placeholder="请输入验证码"
                      size="large"
                      clearable
                      prefix-icon="Key"
                      class="code-input"
                      maxlength="6"
                      @keyup.enter="handleLogin"
                    />
                    <el-button
                      size="large"
                      class="send-code-btn"
                      @click="sendCode"
                      :loading="sendingCode"
                      :disabled="!canSendCode"
                    >
                      {{ codeButtonText }}
                    </el-button>
                  </div>
                </el-form-item>

                <!-- 登录按钮 -->
                <el-form-item class="login-button-item">
                  <el-button
                    type="primary"
                    size="large"
                    class="login-btn"
                    @click="handleLogin"
                    :loading="loading"
                  >
                    {{ loading ? '登录中...' : '立即登录' }}
                  </el-button>
                </el-form-item>

                <!-- 登录提示 -->
                <div class="login-message" v-if="loginMessage">
                  <el-alert
                    :title="loginMessage"
                    :type="loginMessageType"
                    :closable="false"
                    :show-icon="true"
                    size="small"
                  />
                </div>
              </el-form>
            </div>
          </div>

        <!-- 页脚 -->
        <div class="login-footer">
          <div class="features">
            <div class="feature-item">
              <el-icon class="feature-icon"><Check /></el-icon>
              <span>安全可靠</span>
            </div>
            <div class="feature-item">
              <el-icon class="feature-icon"><Check /></el-icon>
              <span>智能管理</span>
            </div>
            <div class="feature-item">
              <el-icon class="feature-icon"><Check /></el-icon>
              <span>高效便捷</span>
            </div>
          </div>
          <p class="copyright">© 2024 叮咚回收管理系统</p>
        </div>
      </div>
    </div>


  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { useRouter } from "vue-router"
import { ElMessage } from 'element-plus'
import { Check, Phone, Key } from '@element-plus/icons-vue'
import { authApi } from '../api/index.js'
import { setToken, setUserInfo } from '@/utils/auth.js'
import url from '@/assets/logo.png'

let router = useRouter()

// 响应式数据
const loginForm = ref({
  telephone: '',
  code: ''
})

const rules = {
  telephone: [
    { required: true, message: '请输入手机号', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
  ],
  code: [
    { required: true, message: '请输入验证码', trigger: 'blur' },
    { pattern: /^\d{6}$/, message: '验证码为6位数字', trigger: 'blur' }
  ]
}

const loginFormRef = ref()
const loading = ref(false)
const sendingCode = ref(false)
const countdown = ref(0)
const loginMessage = ref('')
const loginMessageType = ref('info')

// 计算属性
const canSendCode = computed(() => {
  return loginForm.value.telephone &&
         /^1[3-9]\d{9}$/.test(loginForm.value.telephone) &&
         countdown.value === 0 &&
         !sendingCode.value
})

const codeButtonText = computed(() => {
  if (sendingCode.value) return '发送中...'
  if (countdown.value > 0) return `${countdown.value}s后重发`
  return '发送验证码'
})

// 发送验证码
const sendCode = async () => {
  if (!canSendCode.value) return

  sendingCode.value = true
  loginMessage.value = ''

  try {
    const result = await authApi.sendCode(loginForm.value.telephone)

    if (result.success) {
      ElMessage.success('验证码发送成功')
      startCountdown()
    } else {
      ElMessage.error(result.message || '验证码发送失败')
    }
  } catch (error) {
    console.error('发送验证码失败:', error)
    ElMessage.error('验证码发送失败: ' + error.message)
  } finally {
    sendingCode.value = false
  }
}

// 开始倒计时
const startCountdown = () => {
  countdown.value = 60
  const timer = setInterval(() => {
    countdown.value--
    if (countdown.value <= 0) {
      clearInterval(timer)
    }
  }, 1000)
}

// 处理登录
const handleLogin = async () => {
  // 表单验证
  const valid = await loginFormRef.value?.validate().catch(() => false)
  if (!valid) {
    return
  }

  loading.value = true
  loginMessage.value = ''

  try {
    const result = await authApi.loginWithCode({
      telphone: loginForm.value.telephone,
      code: loginForm.value.code,
      userType: 1
    })

    if (result.success) {
      // 保存token和用户信息
      setToken(result.data.token)
      setUserInfo(result.data.username)

      ElMessage.success('登录成功！')

      // 清理表单
      loginForm.value.telephone = ''
      loginForm.value.code = ''

      router.push("/")
    } else {
      loginMessage.value = result.msg || '登录失败'
      loginMessageType.value = 'error'
      ElMessage.error(result.msg || '登录失败')
    }
  } catch (error) {
    console.error('登录失败:', error)
    loginMessage.value = '登录失败: ' + error.msg
    loginMessageType.value = 'error'
    ElMessage.error('登录失败: ' + error.msg)
  } finally {
    loading.value = false
  }
}

// 组件挂载时的初始化
onMounted(() => {
  // 可以在这里添加一些初始化逻辑
})
</script>

<style scoped>
/* 主容器 */
.login-container {
  height: 100vh;
  width: 100vw;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  position: relative;
  overflow: hidden;
  box-sizing: border-box;
  margin: 0;
  left: 0;
  top: 0;
}

/* 背景装饰 */
.login-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  z-index: -1;
}

.bg-circle {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.08);
  animation: float 8s ease-in-out infinite;
}

.circle-1 {
  width: 300px;
  height: 300px;
  top: -150px;
  left: -150px;
  animation-delay: 0s;
}

.circle-2 {
  width: 200px;
  height: 200px;
  top: 50%;
  right: -100px;
  animation-delay: 3s;
}

.circle-3 {
  width: 150px;
  height: 150px;
  bottom: -75px;
  left: 50%;
  animation-delay: 6s;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) scale(1);
    opacity: 0.8;
  }
  50% {
    transform: translateY(-30px) scale(1.1);
    opacity: 0.6;
  }
}

/* 登录包装器 */
.login-wrapper {
  width: 100%;
  max-width: 420px;
  margin: 0 auto; /* 确保左右居中 */
  z-index: 1;
}

/* 登录卡片 */
.login-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
  padding: 24px 20px 20px 20px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  max-height: calc(100vh - 40px);
  overflow: hidden;
  box-sizing: border-box;
}

/* 品牌头部 */
.login-header {
  text-align: center;
  margin-bottom: 16px;
}

.brand-logo {
  margin-bottom: 8px;
  display: flex;
  justify-content: center;
}

.brand-title {
  font-size: 1.4rem;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 2px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.brand-subtitle {
  font-size: 0.8rem;
  color: #6b7280;
  margin: 0;
}

/* 表单区域 */
.login-form-wrapper {
  margin-bottom: 12px;
}

.form-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: #1f2937;
  text-align: center;
  margin-bottom: 16px;
}

/* 表单样式 */
.login-form {
  width: 100%;
}

.el-form-item {
  margin-bottom: 12px;
}

:deep(.login-input .el-input__wrapper) {
  border-radius: 12px;
  border: 2px solid #e5e7eb;
  transition: all 0.3s ease;
  padding: 14px 16px;
  background: rgba(255, 255, 255, 0.8);
}

:deep(.login-input .el-input__wrapper:hover) {
  border-color: #667eea;
  background: rgba(255, 255, 255, 0.9);
}

:deep(.login-input .el-input__wrapper.is-focus) {
  border-color: #667eea;
  box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.15);
  background: rgba(255, 255, 255, 1);
}

:deep(.login-input .el-input__inner) {
  font-size: 1rem;
  color: #1f2937;
}

:deep(.login-input .el-input__prefix) {
  color: #667eea;
}

/* 验证码区域 */
.captcha-section {
  width: 100%;
}

/* 登录选项 */
.login-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 12px 0;
  font-size: 0.8rem;
}

:deep(.login-options .el-checkbox) {
  color: #6b7280;
}

:deep(.login-options .el-link) {
  font-weight: 500;
}

/* 登录按钮 */
.login-button-item {
  margin-bottom: 0;
}

.login-btn {
  width: 100%;
  height: 50px;
  font-size: 1.1rem;
  font-weight: 600;
  border-radius: 12px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
}

.login-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.login-btn:active {
  transform: translateY(0);
}

.login-btn:disabled {
  opacity: 0.6;
  transform: none;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.2);
}

/* 登录消息 */
.login-message {
  margin-top: 20px;
}

/* 验证码调试信息 - 与登录表单保持一致 */
.captcha-message {
  margin-top: 20px;
}

/* 页脚 */
.login-footer {
  text-align: center;
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px solid rgba(0, 0, 0, 0.08);
}

.features {
  display: flex;
  justify-content: center;
  gap: 16px;
  margin-bottom: 8px;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 3px;
  font-size: 0.7rem;
  color: #6b7280;
}

.feature-icon {
  color: #10b981;
  font-size: 10px;
}

.copyright {
  font-size: 0.65rem;
  color: #9ca3af;
  margin: 0;
}

/* 验证码组件样式调整 */
:deep(.chinese-captcha) {
  width: 100%;
}

:deep(.captcha-title) {
  margin-bottom: 8px;
}

:deep(.title-text) {
  color: #374151;
  font-weight: 500;
}

:deep(.prompt-text) {
  font-size: 0.8rem;
  margin-bottom: 12px;
}

:deep(.captcha-main) {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

:deep(.captcha-image) {
  border-radius: 12px;
}

:deep(.clear-btn) {
  font-size: 0.75rem;
}

:deep(.captcha-loading),
:deep(.captcha-error) {
  padding: 16px;
  border-radius: 8px;
  text-align: center;
  font-size: 0.85rem;
  color: #6b7280;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .login-container {
    padding: 8px;
    height: 100vh;
    align-items: center;
  }

  .login-card {
    padding: 20px 16px 16px 16px;
    border-radius: 12px;
    max-height: calc(100vh - 16px);
    width: 100%;
    max-width: 380px;
  }

  .brand-title {
    font-size: 1.3rem;
  }

  .brand-subtitle {
    font-size: 0.75rem;
  }

  .form-title {
    font-size: 1rem;
    margin-bottom: 14px;
  }

  .login-btn {
    height: 44px;
    font-size: 0.9rem;
  }

  .features {
    gap: 10px;
  }

  .feature-item {
    font-size: 0.65rem;
  }
}

@media (max-width: 480px) {
  .login-container {
    padding: 6px;
  }

  .login-card {
    padding: 16px 12px 12px 12px;
    border-radius: 10px;
    max-height: calc(100vh - 12px);
  }

  .login-header {
    margin-bottom: 12px;
  }

  .brand-logo {
    margin-bottom: 6px;
  }

  .brand-title {
    font-size: 1.2rem;
  }

  .brand-subtitle {
    font-size: 0.7rem;
  }

  .form-title {
    font-size: 0.95rem;
    margin-bottom: 12px;
  }

  .login-options {
    flex-direction: column;
    gap: 6px;
    align-items: flex-start;
    margin: 10px 0;
  }

  .features {
    flex-direction: column;
    gap: 4px;
  }

  .feature-item {
    justify-content: center;
    font-size: 0.6rem;
  }

  :deep(.login-input .el-input__wrapper) {
    padding: 10px 12px;
  }

  :deep(.code-input .el-input__wrapper) {
    padding: 10px 12px;
  }
}

@media (max-width: 360px) {
  .login-container {
    padding: 4px;
  }

  .login-card {
    padding: 14px 10px 10px 10px;
    max-height: calc(100vh - 8px);
  }

  .brand-title {
    font-size: 1.1rem;
  }

  .form-title {
    font-size: 0.9rem;
    margin-bottom: 10px;
  }

  .login-btn {
    height: 42px;
    font-size: 0.85rem;
  }

  .login-footer {
    margin-top: 8px;
    padding-top: 8px;
  }

  .features {
    gap: 2px;
  }

  .feature-item {
    font-size: 0.55rem;
  }

  .copyright {
    font-size: 0.6rem;
  }
}

/* 表单容器 */
.form-container {
  position: relative;
  width: 100%;
  min-height: 280px;
  overflow: visible;
}

/* 登录表单样式 */
.login-form-wrapper {
  width: 100%;
  max-width: 400px;
  margin: 0 auto;
  padding: 0;
}

/* 确保错误提示有足够空间显示 */
:deep(.el-form-item__error) {
  position: static;
  margin-top: 4px;
  margin-bottom: 8px;
  font-size: 12px;
  color: #f56c6c;
  line-height: 1.2;
}

/* 调整表单项间距，为错误提示留出空间 */
:deep(.el-form-item) {
  margin-bottom: 16px;
}

:deep(.el-form-item.is-error) {
  margin-bottom: 20px;
}

/* 验证码输入组样式 */
.code-input-group {
  display: flex;
  gap: 12px;
  align-items: center; /* 确保按钮与输入框垂直居中对齐 */
}

.code-input {
  flex: 1;
}

/* 验证码输入框样式 - 与手机号输入框保持一致 */
:deep(.code-input .el-input__wrapper) {
  border-radius: 12px;
  border: 2px solid #e5e7eb;
  transition: all 0.3s ease;
  padding: 14px 16px;
  background: rgba(255, 255, 255, 0.8);
}

:deep(.code-input .el-input__wrapper:hover) {
  border-color: #667eea;
  background: rgba(255, 255, 255, 0.9);
}

:deep(.code-input .el-input__wrapper.is-focus) {
  border-color: #667eea;
  box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.15);
  background: rgba(255, 255, 255, 1);
}

:deep(.code-input .el-input__inner) {
  font-size: 1rem;
  color: #1f2937;
}

:deep(.code-input .el-input__prefix) {
  color: #667eea;
}

.send-code-btn {
  width: 120px;
  height: 52px; /* 调整为与 Element Plus large 输入框匹配 */
  font-size: 0.9rem;
  font-weight: 600;
  color: white;
  border-radius: 12px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
  white-space: nowrap;
  display: flex;
  align-items: center;
  justify-content: center;
}

.send-code-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.send-code-btn:active:not(:disabled) {
  transform: translateY(0);
}

.send-code-btn:disabled {
  opacity: 0.6;
  transform: none;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.2);
  background: #9ca3af;
}



/* 移动端响应式优化 */
@media (max-width: 768px) {
  .form-container {
    min-height: 260px;
  }

  .send-code-btn {
    width: 100px;
    font-size: 0.8rem;
  }
}

@media (max-width: 480px) {
  .form-container {
    min-height: 240px;
  }

  .code-input-group {
    gap: 8px;
  }

  .send-code-btn {
    width: 90px;
    height: 46px; /* 调整为与移动端输入框匹配 */
    font-size: 0.75rem;
  }
}

@media (max-width: 360px) {
  .form-container {
    min-height: 220px;
  }

  .send-code-btn {
    width: 80px;
    height: 44px; /* 调整为与小屏手机输入框匹配 */
    font-size: 0.7rem;
  }
}
</style>
