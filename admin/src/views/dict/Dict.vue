<template>
  <div class="dict-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>数据字典管理</h2>
      <p>管理系统中的数据字典配置信息</p>
    </div>

    <!-- 搜索和操作区域 -->
    <el-card class="search-card" shadow="never">
      <el-form :model="searchForm" :inline="true" class="search-form">
        <el-form-item label="字典分类">
          <el-select
            v-model="searchForm.dictCategory"
            placeholder="请选择字典分类"
            style="width: 150px"
            clearable
            @change="onCategoryChange"
          >
            <el-option
              v-for="category in categories"
              :key="category"
              :label="category"
              :value="category"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="字典名称">
          <el-input
            v-model="searchForm.dictName"
            placeholder="请输入字典名称"
            clearable
          />
        </el-form-item>

        <el-form-item label="字典内容">
          <el-input
            v-model="searchForm.dictContent"
            placeholder="请输入字典内容"
            clearable
          />
        </el-form-item>

        <el-form-item label="状态">
          <el-select v-model="searchForm.state" placeholder="请选择状态"
                     style="width: 100px" clearable>
            <el-option label="有效" value="1" />
            <el-option label="无效" value="0" />
          </el-select>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="handleSearch" :loading="loading">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>

      <!-- 操作按钮 -->
      <div class="action-buttons">
        <el-button type="primary" @click="handleAdd">
          <el-icon><Plus /></el-icon>
          新增字典
        </el-button>
      </div>
    </el-card>

    <!-- 数据表格 -->
    <el-card class="table-card" shadow="never">
      <el-table
        :data="tableData"
        v-loading="loading"
        @selection-change="handleSelectionChange"
        stripe
        border
        style="width: 100%"
      >
        <el-table-column type="selection" width="55" />

        <el-table-column prop="dictCategory" label="字典分类" width="150" />

        <el-table-column prop="dictName" label="字典名称" width="200" />

        <el-table-column prop="dictContent" label="字典内容" min-width="200" show-overflow-tooltip />

        <el-table-column prop="dictDescription" label="字典描述" show-overflow-tooltip />

        <el-table-column prop="sortOrder" label="排序号" width="100" align="center" />

        <el-table-column prop="state" label="状态" width="80" align="center">
          <template #default="{ row }">
            <el-tag :type="row.state === '1' ? 'success' : 'danger'" size="small">
              {{ row.state === '1' ? '有效' : '无效' }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="creator" label="创建人" width="130" />

        <el-table-column prop="createDate" label="创建时间" width="160">
          <template #default="{ row }">
            {{ formatDateTime(row.createDate) }}
          </template>
        </el-table-column>

        <el-table-column prop="updator" label="修改人" width="130" />

        <el-table-column prop="updateDate" label="修改时间" width="160">
          <template #default="{ row }">
            {{ formatDateTime(row.updateDate) }}
          </template>
        </el-table-column>

        <el-table-column label="操作" :width="isMobile ? 120 : 240" fixed="right" align="center">
          <template #default="{ row }">
            <template v-if="!isMobile">
              <el-button type="primary" size="small" @click="handleEdit(row)">
                编辑
              </el-button>
              <el-button
                :type="row.state === '1' ? 'warning' : 'success'"
                size="small"
                @click="handleToggleState(row)"
              >
                {{ row.state === '1' ? '禁用' : '启用' }}
              </el-button>
              <el-button type="danger" size="small" @click="handleDelete(row)">
                删除
              </el-button>
            </template>
            <template v-else>
              <el-dropdown trigger="click" @command="(cmd) => handleMobileAction(row, cmd)">
                <el-button type="primary" size="small">
                  操作<el-icon class="el-icon--right"><ArrowDown /></el-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item command="edit">编辑</el-dropdown-item>
                    <el-dropdown-item command="toggle">
                      {{ row.state === '1' ? '禁用' : '启用' }}
                    </el-dropdown-item>
                    <el-dropdown-item command="delete" divided>删除</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </template>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.page"
          v-model:page-size="pagination.size"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          :layout="isMobile ? 'total, prev, next' : 'total, sizes, prev, pager, next, jumper'"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 新增/编辑对话框 -->
    <el-dialog
      :title="dialogTitle"
      v-model="dialogVisible"
      :width="isMobile ? '96%' : '600px'"
      :close-on-click-modal="false"
    >
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        label-width="100px"
      >
        <el-row :gutter="20">
          <el-col :span="isMobile ? 24 : 12">
            <el-form-item label="字典分类" prop="dictCategory">
              <el-input v-model="formData.dictCategory" placeholder="请输入字典分类" />
            </el-form-item>
          </el-col>
          <el-col :span="isMobile ? 24 : 12">
            <el-form-item label="字典名称" prop="dictName">
              <el-input v-model="formData.dictName" placeholder="请输入字典名称" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="字典内容" prop="dictContent">
          <el-input v-model="formData.dictContent" placeholder="请输入字典内容" />
        </el-form-item>

        <el-form-item label="字典描述">
          <el-input
            v-model="formData.dictDescription"
            type="textarea"
            :rows="3"
            placeholder="请输入字典描述"
          />
        </el-form-item>

        <el-row :gutter="20">
          <el-col :span="isMobile ? 24 : 12">
            <el-form-item label="排序号" prop="sortOrder">
              <el-input-number
                v-model="formData.sortOrder"
                :min="0"
                :max="9999"
                placeholder="请输入排序号"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="isMobile ? 24 : 12">
            <el-form-item label="状态">
              <el-radio-group v-model="formData.state">
                <el-radio value="1">有效</el-radio>
                <el-radio value="0">无效</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit" :loading="submitLoading">
            确定
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 字典预览对话框 -->
    <el-dialog
      title="字典预览"
      v-model="previewVisible"
      :width="isMobile ? '96%' : '800px'"
    >
      <div class="dict-preview">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="字典分类">{{ previewData.dictCategory }}</el-descriptions-item>
          <el-descriptions-item label="字典名称">{{ previewData.dictName }}</el-descriptions-item>
          <el-descriptions-item label="字典内容" :span="2">{{ previewData.dictContent }}</el-descriptions-item>
          <el-descriptions-item label="字典描述" :span="2">{{ previewData.dictDescription }}</el-descriptions-item>
          <el-descriptions-item label="排序号">{{ previewData.sortOrder }}</el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="previewData.state === '1' ? 'success' : 'danger'" size="small">
              {{ previewData.state === '1' ? '有效' : '无效' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="创建人">{{ previewData.creator }}</el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ formatDateTime(previewData.createDate) }}</el-descriptions-item>
          <el-descriptions-item label="修改人">{{ previewData.updator }}</el-descriptions-item>
          <el-descriptions-item label="修改时间">{{ formatDateTime(previewData.updateDate) }}</el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onBeforeUnmount, nextTick } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, Refresh, Plus, Delete, Check, Close, View, ArrowDown } from '@element-plus/icons-vue'
import { dictionaryApi } from '@/api/dictionary'

// 响应式数据
const loading = ref(false)
const submitLoading = ref(false)
const dialogVisible = ref(false)
const previewVisible = ref(false)
const dialogTitle = ref('')
const isEdit = ref(false)
const currentEditId = ref(null)

// 表格数据
const tableData = ref([])
const selectedRows = ref([])

// 分页数据
const pagination = reactive({
  page: 1,
  size: 10,
  total: 0
})

// 搜索表单
const searchForm = reactive({
  dictCategory: '',
  dictName: '',
  dictContent: '',
  state: ''
})

// 字典分类选项
const categories = ref([])

// 表单数据
const formData = reactive({
  dictCategory: '',
  dictName: '',
  dictContent: '',
  dictDescription: '',
  sortOrder: 0,
  state: '1'
})

// 预览数据
const previewData = reactive({})

// 表单引用
const formRef = ref()

// 表单验证规则
const formRules = {
  dictCategory: [
    { required: true, message: '请输入字典分类', trigger: 'blur' },
    { max: 50, message: '字典分类长度不能超过50个字符', trigger: 'blur' }
  ],
  dictName: [
    { required: true, message: '请输入字典名称', trigger: 'blur' },
    { max: 100, message: '字典名称长度不能超过100个字符', trigger: 'blur' }
  ],
  dictContent: [
    { required: true, message: '请输入字典内容', trigger: 'blur' },
    { max: 500, message: '字典内容长度不能超过500个字符', trigger: 'blur' }
  ],
  sortOrder: [
    { type: 'number', min: 0, max: 9999, message: '排序号必须在0-9999之间', trigger: 'blur' }
  ]
}

// 页面加载时初始化
onMounted(() => {
  loadData()
  loadCategories()
})

// 加载数据
const loadData = async () => {
  loading.value = true
  try {
    const queryParams = { ...searchForm }
    const response = await dictionaryApi.searchDictionaries(queryParams, pagination.page - 1, pagination.size)
    if (response.success) {
      tableData.value = response.data.content || []
      pagination.total = response.data.totalElements || 0
    } else {
      ElMessage.error(response.msg || '加载数据失败')
    }
  } catch (error) {
    console.error('加载数据失败:', error)
    ElMessage.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

// 加载字典分类
const loadCategories = async () => {
  try {
    const response = await dictionaryApi.getAllCategories()
    if (response.success) {
      categories.value = response.data || []
    }
  } catch (error) {
    console.error('加载字典分类失败:', error)
  }
}

// 分类变化处理
const onCategoryChange = () => {
  // 可以在这里添加根据分类过滤的逻辑
}

// 搜索
const handleSearch = () => {
  pagination.page = 1
  loadData()
}

// 重置搜索
const handleReset = () => {
  Object.assign(searchForm, {
    dictCategory: '',
    dictName: '',
    dictContent: '',
    state: ''
  })
  pagination.page = 1
  loadData()
}

// 加载有效字典
const loadActiveDict = async () => {
  loading.value = true
  try {
    const response = await dictionaryApi.getActiveDictionaries()
    if (response.success) {
      tableData.value = response.data || []
      pagination.total = response.data.length || 0
      ElMessage.success('已显示所有有效字典')
    } else {
      ElMessage.error(response.msg || '加载有效字典失败')
    }
  } catch (error) {
    console.error('加载有效字典失败:', error)
    ElMessage.error('加载有效字典失败')
  } finally {
    loading.value = false
  }
}

// 表格选择变化
const handleSelectionChange = (selection) => {
  selectedRows.value = selection
}

// 分页大小变化
const handleSizeChange = (size) => {
  pagination.size = size
  pagination.page = 1
  loadData()
}

// 当前页变化
const handleCurrentChange = (page) => {
  pagination.page = page
  loadData()
}

// 新增
const handleAdd = () => {
  isEdit.value = false
  dialogTitle.value = '新增数据字典'
  resetFormData()
  dialogVisible.value = true
}

// 编辑
const handleEdit = (row) => {
  isEdit.value = true
  dialogTitle.value = '编辑数据字典'
  currentEditId.value = row.id

  // 填充表单数据
  Object.assign(formData, {
    dictCategory: row.dictCategory,
    dictName: row.dictName,
    dictContent: row.dictContent,
    dictDescription: row.dictDescription,
    sortOrder: row.sortOrder || 0,
    state: row.state
  })

  dialogVisible.value = true
}

// 重置表单数据
const resetFormData = () => {
  Object.assign(formData, {
    dictCategory: '',
    dictName: '',
    dictContent: '',
    dictDescription: '',
    sortOrder: 0,
    state: '1'
  })

  nextTick(() => {
    formRef.value?.clearValidate()
  })
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()

    submitLoading.value = true

    let response
    if (isEdit.value) {
      response = await dictionaryApi.updateDictionary(currentEditId.value, formData)
    } else {
      response = await dictionaryApi.createDictionary(formData)
    }

    if (response.success) {
      ElMessage.success(isEdit.value ? '更新成功' : '创建成功')
      dialogVisible.value = false
      loadData()
      loadCategories() // 重新加载分类列表
    } else {
      ElMessage.error(response.msg || (isEdit.value ? '更新失败' : '创建失败'))
    }
  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error('提交失败')
  } finally {
    submitLoading.value = false
  }
}

// 删除
const handleDelete = async (row) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除字典"${row.dictCategory} - ${row.dictName}"吗？`,
      '删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const response = await dictionaryApi.deleteDictionary(row.id)
    if (response.success) {
      ElMessage.success('删除成功')
      loadData()
    } else {
      ElMessage.error(response.msg || '删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

// 批量删除
const handleBatchDelete = async () => {
  if (!selectedRows.value.length) {
    ElMessage.warning('请选择要删除的数据')
    return
  }

  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedRows.value.length} 条字典吗？`,
      '批量删除确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const ids = selectedRows.value.map(row => row.id)
    const response = await dictionaryApi.deleteDictionaries(ids)
    if (response.success) {
      ElMessage.success('批量删除成功')
      loadData()
    } else {
      ElMessage.error(response.msg || '批量删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量删除失败:', error)
      ElMessage.error('批量删除失败')
    }
  }
}

// 批量启用
const handleBatchEnable = async () => {
  if (!selectedRows.value.length) {
    ElMessage.warning('请选择要启用的数据')
    return
  }

  try {
    await ElMessageBox.confirm(
      `确定要启用选中的 ${selectedRows.value.length} 条字典吗？`,
      '批量启用确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'info'
      }
    )

    const ids = selectedRows.value.map(row => row.id)
    const response = await dictionaryApi.enableDictionaries(ids)
    if (response.success) {
      ElMessage.success('批量启用成功')
      loadData()
    } else {
      ElMessage.error(response.msg || '批量启用失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量启用失败:', error)
      ElMessage.error('批量启用失败')
    }
  }
}

// 批量禁用
const handleBatchDisable = async () => {
  if (!selectedRows.value.length) {
    ElMessage.warning('请选择要禁用的数据')
    return
  }

  try {
    await ElMessageBox.confirm(
      `确定要禁用选中的 ${selectedRows.value.length} 条字典吗？`,
      '批量禁用确认',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const ids = selectedRows.value.map(row => row.id)
    const response = await dictionaryApi.disableDictionaries(ids)
    if (response.success) {
      ElMessage.success('批量禁用成功')
      loadData()
    } else {
      ElMessage.error(response.msg || '批量禁用失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量禁用失败:', error)
      ElMessage.error('批量禁用失败')
    }
  }
}

// 切换状态（启用/禁用）
const handleToggleState = async (row) => {
  const action = row.state === '1' ? '禁用' : '启用'

  try {
    await ElMessageBox.confirm(
      `确定要${action}字典"${row.dictCategory} - ${row.dictName}"吗？`,
      `${action}确认`,
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    let response
    if (row.state === '1') {
      response = await dictionaryApi.disableDictionary(row.id)
    } else {
      response = await dictionaryApi.enableDictionary(row.id)
    }

    if (response.success) {
      ElMessage.success(`${action}成功`)
      loadData()
    } else {
      ElMessage.error(response.msg || `${action}失败`)
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error(`${action}失败:`, error)
      ElMessage.error(`${action}失败`)
    }
  }
}

// 预览字典
const handlePreview = (row) => {
  Object.assign(previewData, row)
  previewVisible.value = true
}

// 格式化日期时间
const formatDateTime = (dateTime) => {
  if (!dateTime) return ''
  return new Date(dateTime).toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// ============== 移动端适配 ==============
const isMobile = ref(false)
const updateIsMobile = () => {
  isMobile.value = window.innerWidth <= 768
}
onMounted(() => {
  updateIsMobile()
  window.addEventListener('resize', updateIsMobile)
})
onBeforeUnmount(() => {
  window.removeEventListener('resize', updateIsMobile)
})

// 移动端折叠菜单统一处理
const handleMobileAction = (row, cmd) => {
  switch (cmd) {
    case 'edit':
      handleEdit(row)
      break
    case 'toggle':
      handleToggleState(row)
      break
    case 'delete':
      handleDelete(row)
      break
  }
}
</script>

<style scoped>
.dict-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 60px);
  overflow-y: auto;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  color: #303133;
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
}

.page-header p {
  color: #909399;
  margin: 0;
  font-size: 14px;
}

.search-card {
  margin-bottom: 20px;
}

.search-form {
  margin-bottom: 20px;
}

.action-buttons {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.table-card {
  background: white;
  border-radius: 8px;
  overflow: hidden;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
  padding: 20px 0;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.dict-preview {
  padding: 10px 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .dict-container {
    padding: 10px;
  }

  .search-form {
    flex-direction: column;
  }

  .search-form .el-form-item {
    margin-right: 0;
    margin-bottom: 15px;
  }

  .action-buttons {
    justify-content: center;
  }

  .el-table {
    font-size: 12px;
  }

  /* 表格在移动端更紧凑 */
  :deep(.el-table .el-table__cell) {
    padding: 8px 0;
  }
}

/* 表格样式优化 */
:deep(.el-table) {
  border-radius: 8px;
  overflow: hidden;
}

:deep(.el-table th) {
  background-color: #fafafa;
  color: #606266;
  font-weight: 600;
}

:deep(.el-table td) {
  border-bottom: 1px solid #ebeef5;
}

:deep(.el-table tr:hover > td) {
  background-color: #f5f7fa;
}

/* 标签样式 */
:deep(.el-tag) {
  border-radius: 4px;
  font-weight: 500;
}

/* 按钮样式 */
:deep(.el-button) {
  border-radius: 6px;
  font-weight: 500;
}

:deep(.el-button--small) {
  padding: 5px 12px;
  font-size: 12px;
}

/* 对话框样式 */
:deep(.el-dialog) {
  border-radius: 12px;
  overflow: hidden;
}

:deep(.el-dialog__header) {
  background-color: #fafafa;
  padding: 20px 24px 16px;
  border-bottom: 1px solid #ebeef5;
}

:deep(.el-dialog__body) {
  padding: 24px;
}

:deep(.el-dialog__footer) {
  padding: 16px 24px 24px;
  border-top: 1px solid #ebeef5;
}

/* 表单样式 */
:deep(.el-form-item__label) {
  font-weight: 500;
  color: #606266;
}

:deep(.el-input__wrapper) {
  border-radius: 6px;
}

:deep(.el-select .el-input__wrapper) {
  border-radius: 6px;
}

:deep(.el-textarea__inner) {
  border-radius: 6px;
}

/* 卡片样式 */
:deep(.el-card) {
  border-radius: 12px;
  border: 1px solid #ebeef5;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

:deep(.el-card__body) {
  padding: 24px;
}

/* 分页样式 */
:deep(.el-pagination) {
  justify-content: center;
}

:deep(.el-pagination .el-pager li) {
  border-radius: 6px;
  margin: 0 2px;
}

:deep(.el-pagination .btn-prev),
:deep(.el-pagination .btn-next) {
  border-radius: 6px;
}

/* 描述列表样式 */
:deep(.el-descriptions) {
  margin-top: 10px;
}

:deep(.el-descriptions__label) {
  font-weight: 600;
  color: #606266;
}

:deep(.el-descriptions__content) {
  color: #303133;
}
</style>
