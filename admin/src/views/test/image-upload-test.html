<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图片上传功能测试</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .upload-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background-color: #fafafa;
        }
        .upload-title {
            font-size: 18px;
            font-weight: bold;
            color: #409eff;
            margin-bottom: 15px;
        }
        .upload-area {
            border: 2px dashed #ccc;
            border-radius: 8px;
            padding: 40px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 20px;
        }
        .upload-area:hover {
            border-color: #409eff;
            background-color: #f0f9ff;
        }
        .upload-area.dragover {
            border-color: #67c23a;
            background-color: #f0f9ff;
        }
        .file-input {
            display: none;
        }
        .upload-button {
            background-color: #409eff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin: 10px;
        }
        .upload-button:hover {
            background-color: #66b1ff;
        }
        .result-section {
            margin-top: 20px;
            padding: 15px;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
        }
        .success {
            background-color: #f0f9ff;
            border: 1px solid #b3e5fc;
            color: #0277bd;
        }
        .error {
            background-color: #fff3e0;
            border: 1px solid #ffcc02;
            color: #f57c00;
        }
        .info {
            background-color: #e8f5e8;
            border: 1px solid #4caf50;
            color: #2e7d32;
        }
        .image-preview {
            display: flex;
            gap: 20px;
            margin-top: 20px;
            flex-wrap: wrap;
        }
        .image-item {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 10px;
            background: white;
            text-align: center;
        }
        .image-item img {
            max-width: 200px;
            max-height: 200px;
            border-radius: 4px;
        }
        .image-info {
            margin-top: 10px;
            font-size: 12px;
            color: #666;
        }
        .progress-bar {
            width: 100%;
            height: 20px;
            background-color: #f0f0f0;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        .progress-fill {
            height: 100%;
            background-color: #409eff;
            width: 0%;
            transition: width 0.3s ease;
        }
        .file-info {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
            font-size: 14px;
        }
        .validation-rules {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 4px;
            padding: 15px;
            margin-bottom: 20px;
        }
        .validation-rules h4 {
            margin: 0 0 10px 0;
            color: #856404;
        }
        .validation-rules ul {
            margin: 0;
            padding-left: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📸 图片上传功能测试</h1>
        
        <!-- 验证规则说明 -->
        <div class="validation-rules">
            <h4>📋 上传规则</h4>
            <ul>
                <li>只支持图片格式：JPEG, JPG, PNG, GIF, BMP, WEBP</li>
                <li>文件大小限制：最大 10MB</li>
                <li>自动生成 100x100 像素缩略图</li>
                <li>支持图片压缩（超过 1MB 自动压缩）</li>
                <li>文件名自动生成 UUID 避免冲突</li>
            </ul>
        </div>

        <!-- 新版图片上传接口测试 -->
        <div class="upload-section">
            <div class="upload-title">1. 新版图片上传接口 (/upload/image)</div>
            <p>返回详细的图片信息，包括原图、缩略图、压缩图等</p>
            
            <div class="upload-area" id="uploadArea1" onclick="document.getElementById('fileInput1').click()">
                <p>📁 点击选择图片文件或拖拽到此处</p>
                <p style="color: #666; font-size: 12px;">支持 JPG, PNG, GIF, BMP, WEBP 格式</p>
            </div>
            
            <input type="file" id="fileInput1" class="file-input" accept="image/*" onchange="uploadImage(this.files[0], 'new')">
            
            <div class="progress-bar" id="progressBar1" style="display: none;">
                <div class="progress-fill" id="progressFill1"></div>
            </div>
            
            <div id="result1" class="result-section"></div>
            <div id="preview1" class="image-preview"></div>
        </div>

        <!-- 兼容旧接口测试 -->
        <div class="upload-section">
            <div class="upload-title">2. 兼容旧接口测试 (/upload)</div>
            <p>保持向后兼容，只返回原图地址</p>
            
            <div class="upload-area" id="uploadArea2" onclick="document.getElementById('fileInput2').click()">
                <p>📁 点击选择图片文件或拖拽到此处</p>
                <p style="color: #666; font-size: 12px;">测试旧接口兼容性</p>
            </div>
            
            <input type="file" id="fileInput2" class="file-input" accept="image/*" onchange="uploadImage(this.files[0], 'old')">
            
            <div class="progress-bar" id="progressBar2" style="display: none;">
                <div class="progress-fill" id="progressFill2"></div>
            </div>
            
            <div id="result2" class="result-section"></div>
            <div id="preview2" class="image-preview"></div>
        </div>

        <!-- 错误测试 -->
        <div class="upload-section">
            <div class="upload-title">3. 错误情况测试</div>
            <p>测试各种错误情况的处理</p>
            
            <button class="upload-button" onclick="testEmptyFile()">测试空文件</button>
            <button class="upload-button" onclick="testLargeFile()">测试大文件</button>
            <button class="upload-button" onclick="testInvalidFormat()">测试非图片文件</button>
            
            <div id="errorResult" class="result-section"></div>
        </div>

        <!-- 批量上传测试 -->
        <div class="upload-section">
            <div class="upload-title">4. 批量上传测试</div>
            <p>测试多个文件同时上传</p>
            
            <div class="upload-area" id="uploadArea3" onclick="document.getElementById('fileInput3').click()">
                <p>📁 选择多个图片文件</p>
            </div>
            
            <input type="file" id="fileInput3" class="file-input" accept="image/*" multiple onchange="uploadMultipleImages(this.files)">
            
            <div id="batchResult" class="result-section"></div>
            <div id="batchPreview" class="image-preview"></div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8081';

        // 拖拽上传支持
        ['uploadArea1', 'uploadArea2', 'uploadArea3'].forEach(id => {
            const area = document.getElementById(id);
            
            area.addEventListener('dragover', (e) => {
                e.preventDefault();
                area.classList.add('dragover');
            });
            
            area.addEventListener('dragleave', () => {
                area.classList.remove('dragover');
            });
            
            area.addEventListener('drop', (e) => {
                e.preventDefault();
                area.classList.remove('dragover');
                
                const files = e.dataTransfer.files;
                if (files.length > 0) {
                    if (id === 'uploadArea3') {
                        uploadMultipleImages(files);
                    } else {
                        const apiType = id === 'uploadArea1' ? 'new' : 'old';
                        uploadImage(files[0], apiType);
                    }
                }
            });
        });

        // 上传图片
        async function uploadImage(file, apiType) {
            if (!file) return;

            const resultId = apiType === 'new' ? 'result1' : 'result2';
            const previewId = apiType === 'new' ? 'preview1' : 'preview2';
            const progressBarId = apiType === 'new' ? 'progressBar1' : 'progressBar2';
            const progressFillId = apiType === 'new' ? 'progressFill1' : 'progressFill2';
            
            const resultDiv = document.getElementById(resultId);
            const previewDiv = document.getElementById(previewId);
            const progressBar = document.getElementById(progressBarId);
            const progressFill = document.getElementById(progressFillId);

            // 显示文件信息
            resultDiv.innerHTML = `上传文件信息：
文件名: ${file.name}
文件大小: ${formatFileSize(file.size)}
文件类型: ${file.type}
最后修改: ${new Date(file.lastModified).toLocaleString()}

开始上传...`;
            resultDiv.className = 'result-section info';

            // 显示进度条
            progressBar.style.display = 'block';
            progressFill.style.width = '0%';

            try {
                const formData = new FormData();
                formData.append('file', file);

                const url = apiType === 'new' ? '/upload/image' : '/upload';
                
                const xhr = new XMLHttpRequest();
                
                // 进度监听
                xhr.upload.addEventListener('progress', (e) => {
                    if (e.lengthComputable) {
                        const percentComplete = (e.loaded / e.total) * 100;
                        progressFill.style.width = percentComplete + '%';
                    }
                });

                xhr.onload = function() {
                    progressBar.style.display = 'none';
                    
                    if (xhr.status === 200) {
                        const response = JSON.parse(xhr.responseText);
                        
                        if (response.success) {
                            resultDiv.textContent = JSON.stringify(response, null, 2);
                            resultDiv.className = 'result-section success';
                            
                            // 显示图片预览
                            displayImagePreview(response.data, previewDiv, apiType);
                        } else {
                            resultDiv.textContent = `上传失败: ${response.msg}`;
                            resultDiv.className = 'result-section error';
                        }
                    } else {
                        resultDiv.textContent = `HTTP错误: ${xhr.status} ${xhr.statusText}`;
                        resultDiv.className = 'result-section error';
                    }
                };

                xhr.onerror = function() {
                    progressBar.style.display = 'none';
                    resultDiv.textContent = '网络错误，请检查服务器连接';
                    resultDiv.className = 'result-section error';
                };

                xhr.open('POST', API_BASE + url);
                xhr.send(formData);

            } catch (error) {
                progressBar.style.display = 'none';
                resultDiv.textContent = `上传异常: ${error.message}`;
                resultDiv.className = 'result-section error';
            }
        }

        // 显示图片预览
        function displayImagePreview(data, previewDiv, apiType) {
            previewDiv.innerHTML = '';

            if (apiType === 'new' && data.originalUrl) {
                // 新接口返回详细信息
                const originalDiv = createImagePreview(
                    API_BASE + data.originalUrl,
                    '原图',
                    `${data.width}x${data.height} | ${formatFileSize(data.fileSize)}`
                );
                previewDiv.appendChild(originalDiv);

                if (data.thumbnailUrl) {
                    const thumbnailDiv = createImagePreview(
                        API_BASE + data.thumbnailUrl,
                        '缩略图',
                        '100x100'
                    );
                    previewDiv.appendChild(thumbnailDiv);
                }

                if (data.compressedUrl) {
                    const compressedDiv = createImagePreview(
                        API_BASE + data.compressedUrl,
                        '压缩图',
                        '质量: 80%'
                    );
                    previewDiv.appendChild(compressedDiv);
                }
            } else if (apiType === 'old' && data) {
                // 旧接口只返回URL字符串
                const originalDiv = createImagePreview(
                    API_BASE + data,
                    '原图',
                    '兼容模式'
                );
                previewDiv.appendChild(originalDiv);
            }
        }

        // 创建图片预览元素
        function createImagePreview(src, title, info) {
            const div = document.createElement('div');
            div.className = 'image-item';
            
            const img = document.createElement('img');
            img.src = src;
            img.alt = title;
            img.onerror = function() {
                this.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZGRkIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzk5OSIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPuWKoOi9veWksei0pTwvdGV4dD48L3N2Zz4=';
            };
            
            const titleDiv = document.createElement('div');
            titleDiv.style.fontWeight = 'bold';
            titleDiv.textContent = title;
            
            const infoDiv = document.createElement('div');
            infoDiv.className = 'image-info';
            infoDiv.textContent = info;
            
            div.appendChild(img);
            div.appendChild(titleDiv);
            div.appendChild(infoDiv);
            
            return div;
        }

        // 批量上传
        async function uploadMultipleImages(files) {
            const resultDiv = document.getElementById('batchResult');
            const previewDiv = document.getElementById('batchPreview');
            
            resultDiv.textContent = `开始批量上传 ${files.length} 个文件...\n`;
            resultDiv.className = 'result-section info';
            previewDiv.innerHTML = '';

            for (let i = 0; i < files.length; i++) {
                const file = files[i];
                resultDiv.textContent += `\n正在上传第 ${i + 1} 个文件: ${file.name}\n`;

                try {
                    const formData = new FormData();
                    formData.append('file', file);

                    const response = await fetch(API_BASE + '/upload/image', {
                        method: 'POST',
                        body: formData
                    });

                    const result = await response.json();

                    if (result.success) {
                        resultDiv.textContent += `✅ ${file.name} 上传成功\n`;
                        
                        // 添加预览
                        if (result.data.originalUrl) {
                            const previewItem = createImagePreview(
                                API_BASE + result.data.originalUrl,
                                file.name,
                                formatFileSize(file.size)
                            );
                            previewDiv.appendChild(previewItem);
                        }
                    } else {
                        resultDiv.textContent += `❌ ${file.name} 上传失败: ${result.msg}\n`;
                    }
                } catch (error) {
                    resultDiv.textContent += `❌ ${file.name} 上传异常: ${error.message}\n`;
                }
            }

            resultDiv.textContent += `\n批量上传完成！`;
            resultDiv.className = 'result-section success';
        }

        // 错误测试
        function testEmptyFile() {
            const resultDiv = document.getElementById('errorResult');
            resultDiv.textContent = '测试空文件上传...';
            resultDiv.className = 'result-section info';

            const formData = new FormData();
            // 不添加文件，测试空文件情况

            fetch(API_BASE + '/upload/image', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(result => {
                resultDiv.textContent = `空文件测试结果:\n${JSON.stringify(result, null, 2)}`;
                resultDiv.className = result.success ? 'result-section success' : 'result-section error';
            })
            .catch(error => {
                resultDiv.textContent = `空文件测试异常: ${error.message}`;
                resultDiv.className = 'result-section error';
            });
        }

        function testLargeFile() {
            const resultDiv = document.getElementById('errorResult');
            resultDiv.textContent = '大文件测试需要手动选择超过10MB的图片文件';
            resultDiv.className = 'result-section info';
        }

        function testInvalidFormat() {
            const resultDiv = document.getElementById('errorResult');
            resultDiv.textContent = '非图片格式测试需要手动选择非图片文件（如.txt, .pdf等）';
            resultDiv.className = 'result-section info';
        }

        // 工具函数
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }
    </script>
</body>
</html>
