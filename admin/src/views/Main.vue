<script setup>
import { ref, onMounted, computed } from 'vue'
import { useRouter } from "vue-router"
import { ElMessage, ElMessageBox } from 'element-plus'
import { User, SwitchButton } from '@element-plus/icons-vue'
import menus from "@/router/menu.js"
import { getUserInfo, logout, isLoggedIn } from '@/utils/auth.js'

let router = useRouter()
const menuIndex = ref('dashboard')

// 用户信息相关
const userInfo = ref(null)

// 计算属性：获取用户名
const userName = computed(() => {
  return userInfo.value || '-'
})

// 计算属性：获取用户头像或默认头像
const userAvatar = computed(() => {
  return userInfo.value || ''
})

const handleSelect = (index) => {
  console.info(index)
  router.push("/" + index)
  menuIndex.value = index
}

const menuItems = ref([])
menus.forEach((item) => {
  let _menu = {
    title: item.meta.title,
    path: item.path,
    code: item.meta.code,
  }
  if (item.children) {
    _menu.children = []
    item.children.forEach((subItem) => {
      _menu.children.push({
        title: subItem.meta.title,
        path: subItem.path,
        code: subItem.meta.code,
      })
    })
  }
  menuItems.value.push(_menu)
})

// 处理登出
const handleLogout = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要退出登录吗？',
      '提示',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    // 执行登出
    logout()
    ElMessage.success('已成功退出登录')
  } catch (error) {
    // 用户取消登出
    console.log('用户取消登出')
  }
}

// 组件挂载时检查登录状态和获取用户信息
onMounted(() => {
  // 检查登录状态
  if (!isLoggedIn()) {
    router.push('/login')
    return
  }

  // 获取用户信息
  userInfo.value = getUserInfo()

  // 如果没有用户信息，跳转到登录页
  if (!userInfo.value) {
    ElMessage.error('用户信息获取失败，请重新登录')
    router.push('/login')
  }
})
</script>

<template>
  <div class="common-layout">
    <el-container>
      <el-header class="main-header">
        <div class="header-left">
          <el-menu
            :default-active="menuIndex"
            class="el-menu-demo"
            mode="horizontal"
            @select="handleSelect"
          >
            <el-menu-item index="dashboard">
              <template #title>首页</template>
            </el-menu-item>
            <el-sub-menu v-for="item in menuItems" :index="item.code">
              <template #title>{{ item.title }}</template>
              <el-menu-item
                :index="item.path + '/' + subItem.path"
                v-if="item.children"
                v-for="subItem in item.children"
              >
                {{ subItem.title }}
              </el-menu-item>
            </el-sub-menu>
          </el-menu>
        </div>

        <!-- 用户信息区域 -->
        <div class="header-right">
          <div class="user-info">
            <el-dropdown @command="handleLogout" trigger="click">
              <div class="user-profile">
                <el-avatar
                  :size="32"
                  :src="userAvatar"
                  class="user-avatar"
                >
                  <el-icon><User /></el-icon>
                </el-avatar>
                <span class="user-name">{{ userName }}</span>
                <el-icon class="dropdown-icon"><SwitchButton /></el-icon>
              </div>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="logout">
                    <el-icon><SwitchButton /></el-icon>
                    <span>退出登录</span>
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </div>
      </el-header>

      <el-main style="max-height: 100vh;">
        <router-view></router-view>
      </el-main>
    </el-container>
  </div>
</template>

<style scoped>
.common-layout {
  height: 100vh;
}

.main-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 20px;
  background: #fff;
  border-bottom: 1px solid #e4e7ed;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.header-left {
  flex: 1;
}

.header-right {
  display: flex;
  align-items: center;
}

.user-info {
  display: flex;
  align-items: center;
}

.user-profile {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: rgba(102, 126, 234, 0.05);
  border: 1px solid rgba(102, 126, 234, 0.1);
}

.user-profile:hover {
  background: rgba(102, 126, 234, 0.1);
  border-color: rgba(102, 126, 234, 0.2);
  transform: translateY(-1px);
}

.user-avatar {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.user-name {
  font-size: 14px;
  font-weight: 500;
  color: #1f2937;
  margin: 0 4px;
}

.dropdown-icon {
  font-size: 12px;
  color: #6b7280;
  transition: transform 0.3s ease;
}

.user-profile:hover .dropdown-icon {
  transform: rotate(180deg);
}

/* 菜单样式调整 */
:deep(.el-menu) {
  border-bottom: none;
  background: transparent;
}

:deep(.el-menu--horizontal > .el-menu-item) {
  border-bottom: none;
  color: #1f2937;
  font-weight: 500;
}

:deep(.el-menu--horizontal > .el-menu-item:hover) {
  background: rgba(102, 126, 234, 0.1);
  color: #667eea;
}

:deep(.el-menu--horizontal > .el-menu-item.is-active) {
  color: #667eea;
  border-bottom: 2px solid #667eea;
}

:deep(.el-sub-menu__title) {
  color: #1f2937;
  font-weight: 500;
}

:deep(.el-sub-menu__title:hover) {
  background: rgba(102, 126, 234, 0.1);
  color: #667eea;
}

/* 下拉菜单样式 */
:deep(.el-dropdown-menu__item) {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  color: #1f2937;
}

:deep(.el-dropdown-menu__item:hover) {
  background: rgba(102, 126, 234, 0.1);
  color: #667eea;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .main-header {
    padding: 0 12px;
  }

  .user-name {
    display: none; /* 在移动端隐藏用户名 */
  }

  .user-profile {
    padding: 6px 8px;
  }
}

@media (max-width: 480px) {
  .main-header {
    padding: 0 8px;
  }

  .header-left {
    flex: 0.8;
  }

  .header-right {
    flex: 0.2;
  }
}
</style>
