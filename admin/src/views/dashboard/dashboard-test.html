<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>仪表板功能测试</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .api-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background-color: #fafafa;
        }
        .api-title {
            font-size: 18px;
            font-weight: bold;
            color: #409eff;
            margin-bottom: 15px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #555;
        }
        select {
            width: 200px;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        button {
            background-color: #409eff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        button:hover {
            background-color: #66b1ff;
        }
        button.success {
            background-color: #67c23a;
        }
        button.success:hover {
            background-color: #85ce61;
        }
        .result {
            margin-top: 15px;
            padding: 15px;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
        }
        .success {
            background-color: #f0f9ff;
            border: 1px solid #b3e5fc;
            color: #0277bd;
        }
        .error {
            background-color: #fff3e0;
            border: 1px solid #ffcc02;
            color: #f57c00;
        }
        .info {
            background-color: #e8f5e8;
            border: 1px solid #4caf50;
            color: #2e7d32;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
        }
        .stat-card h3 {
            margin: 0 0 10px 0;
            font-size: 24px;
        }
        .stat-card p {
            margin: 0;
            opacity: 0.9;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📊 仪表板功能测试</h1>
        
        <!-- 获取统计数据 -->
        <div class="api-section">
            <div class="api-title">1. 获取仪表板统计数据</div>
            <p>获取当日订单量、交易额、新增客户等统计信息</p>
            <button onclick="getStatistics()" class="success">获取统计数据</button>
            <div id="statisticsResult" class="result"></div>
            <div id="statisticsCards" class="stats-grid" style="display: none;"></div>
        </div>

        <!-- 获取趋势数据 -->
        <div class="api-section">
            <div class="api-title">2. 获取趋势图数据</div>
            <p>获取不同时间周期的订单量、交易额、新增客户趋势数据</p>
            <div class="form-group">
                <label>选择时间周期:</label>
                <select id="periodSelect">
                    <option value="week">最近一周</option>
                    <option value="month">最近一月</option>
                    <option value="year">最近一年</option>
                </select>
            </div>
            <button onclick="getTrendData()">获取趋势数据</button>
            <div id="trendResult" class="result"></div>
        </div>

        <!-- 创建测试数据 -->
        <div class="api-section">
            <div class="api-title">3. 创建测试数据</div>
            <p>为了测试仪表板功能，可以创建一些测试订单数据</p>
            <button onclick="createTestData()">创建测试数据</button>
            <div id="testDataResult" class="result"></div>
        </div>

        <!-- 完整测试流程 -->
        <div class="api-section">
            <div class="api-title">4. 完整测试流程</div>
            <p>自动执行完整的仪表板功能测试</p>
            <button onclick="runFullTest()" style="background-color: #f56c6c; font-size: 16px; padding: 15px 30px;">
                🚀 执行完整测试
            </button>
            <div id="fullTestResult" class="result"></div>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:8081';

        // 通用请求函数
        async function apiRequest(url, options = {}) {
            try {
                const response = await fetch(API_BASE + url, {
                    headers: {
                        'Content-Type': 'application/json',
                        ...options.headers
                    },
                    ...options
                });
                
                const result = await response.json();
                return result;
            } catch (error) {
                return { success: false, msg: '请求失败: ' + error.message };
            }
        }

        // 显示结果
        function showResult(elementId, result, type = 'info') {
            const element = document.getElementById(elementId);
            element.textContent = JSON.stringify(result, null, 2);
            element.className = `result ${type}`;
        }

        // 1. 获取统计数据
        async function getStatistics() {
            const result = await apiRequest('/api/recycle-orders/dashboard/statistics');
            showResult('statisticsResult', result, result.success ? 'success' : 'error');
            
            if (result.success) {
                displayStatisticsCards(result.data);
            }
            
            return result;
        }

        // 显示统计卡片
        function displayStatisticsCards(data) {
            const cardsContainer = document.getElementById('statisticsCards');
            cardsContainer.style.display = 'grid';
            cardsContainer.innerHTML = `
                <div class="stat-card">
                    <h3>${data.todayOrderCount || 0}</h3>
                    <p>今日订单</p>
                </div>
                <div class="stat-card">
                    <h3>¥${formatAmount(data.todayTransactionAmount)}</h3>
                    <p>今日交易额</p>
                </div>
                <div class="stat-card">
                    <h3>${data.todayNewCustomers || 0}</h3>
                    <p>今日新增客户</p>
                </div>
                <div class="stat-card">
                    <h3>${data.totalOrderCount || 0}</h3>
                    <p>总订单数</p>
                </div>
                <div class="stat-card">
                    <h3>¥${formatAmount(data.totalTransactionAmount)}</h3>
                    <p>总交易额</p>
                </div>
                <div class="stat-card">
                    <h3>${data.totalCustomers || 0}</h3>
                    <p>总客户数</p>
                </div>
            `;
        }

        // 格式化金额
        function formatAmount(amount) {
            if (!amount) return '0.00';
            return Number(amount).toLocaleString('zh-CN', {
                minimumFractionDigits: 2,
                maximumFractionDigits: 2
            });
        }

        // 2. 获取趋势数据
        async function getTrendData() {
            const period = document.getElementById('periodSelect').value;
            const result = await apiRequest(`/api/recycle-orders/dashboard/trends?period=${period}`);
            showResult('trendResult', result, result.success ? 'success' : 'error');
            return result;
        }

        // 3. 创建测试数据
        async function createTestData() {
            const resultDiv = document.getElementById('testDataResult');
            resultDiv.textContent = '开始创建测试数据...\n';
            resultDiv.className = 'result info';

            try {
                // 创建多个测试订单
                const testOrders = [];
                for (let i = 0; i < 5; i++) {
                    const testOrder = {
                        contactName: '测试用户' + (i + 1),
                        contactPhone: '138' + Math.floor(Math.random() * 100000000).toString().padStart(8, '0'),
                        province: '广东省',
                        city: '深圳市',
                        district: '南山区',
                        detailedAddress: '科技园南区深圳湾科技生态园' + (i + 1) + '栋',
                        itemCategory: ['废纸', '废塑料', '废金属', '电子废料'][i % 4],
                        estimatedWeight: Math.floor(Math.random() * 50) + 10,
                        appointmentDate: '2024-12-25',
                        appointmentTimeSlot: ['上午', '下午', '晚上'][i % 3],
                        remark: '测试订单，用于仪表板功能测试'
                    };

                    const result = await apiRequest('/api/recycle-orders', {
                        method: 'POST',
                        body: JSON.stringify(testOrder)
                    });

                    if (result.success) {
                        testOrders.push(result.data);
                        resultDiv.textContent += `✅ 创建订单 ${i + 1}: ${result.data.orderNo}\n`;
                        
                        // 为部分订单添加确认信息
                        if (i < 3) {
                            const confirmResult = await apiRequest('/api/recycle-orders/confirm', {
                                method: 'POST',
                                body: JSON.stringify({
                                    orderId: result.data.id,
                                    confirmWeight: testOrder.estimatedWeight + Math.random() * 5 - 2.5,
                                    transactionAmount: (testOrder.estimatedWeight * (2 + Math.random() * 3)).toFixed(2),
                                    confirmRemark: '测试确认'
                                })
                            });
                            
                            if (confirmResult.success) {
                                resultDiv.textContent += `✅ 确认订单 ${i + 1}: ¥${confirmResult.data.transactionAmount}\n`;
                            }
                        }
                        
                        // 添加延迟避免请求过快
                        await new Promise(resolve => setTimeout(resolve, 500));
                    } else {
                        resultDiv.textContent += `❌ 创建订单 ${i + 1} 失败: ${result.msg}\n`;
                    }
                }

                resultDiv.textContent += `\n🎉 测试数据创建完成！共创建 ${testOrders.length} 个订单\n`;
                resultDiv.className = 'result success';

            } catch (error) {
                resultDiv.textContent += `\n❌ 创建测试数据失败: ${error.message}\n`;
                resultDiv.className = 'result error';
            }
        }

        // 4. 完整测试流程
        async function runFullTest() {
            const resultDiv = document.getElementById('fullTestResult');
            resultDiv.textContent = '开始执行完整仪表板测试...\n';
            resultDiv.className = 'result info';

            try {
                // 步骤1: 获取统计数据
                resultDiv.textContent += '\n步骤1: 获取统计数据...\n';
                const statsResult = await getStatistics();
                if (!statsResult.success) {
                    throw new Error('获取统计数据失败: ' + statsResult.msg);
                }
                resultDiv.textContent += '✅ 统计数据获取成功\n';

                // 步骤2: 测试不同周期的趋势数据
                const periods = ['week', 'month', 'year'];
                for (const period of periods) {
                    resultDiv.textContent += `\n步骤2.${periods.indexOf(period) + 1}: 获取${period}趋势数据...\n`;
                    document.getElementById('periodSelect').value = period;
                    const trendResult = await getTrendData();
                    if (!trendResult.success) {
                        throw new Error(`获取${period}趋势数据失败: ` + trendResult.msg);
                    }
                    resultDiv.textContent += `✅ ${period}趋势数据获取成功，数据点数: ${trendResult.data.dates?.length || 0}\n`;
                    
                    // 添加延迟
                    await new Promise(resolve => setTimeout(resolve, 500));
                }

                resultDiv.textContent += '\n🎉 完整仪表板测试成功！\n';
                resultDiv.textContent += '\n📊 测试结果总结:\n';
                resultDiv.textContent += `- 今日订单: ${statsResult.data.todayOrderCount}\n`;
                resultDiv.textContent += `- 今日交易额: ¥${formatAmount(statsResult.data.todayTransactionAmount)}\n`;
                resultDiv.textContent += `- 今日新增客户: ${statsResult.data.todayNewCustomers}\n`;
                resultDiv.textContent += `- 总订单数: ${statsResult.data.totalOrderCount}\n`;
                resultDiv.textContent += `- 总交易额: ¥${formatAmount(statsResult.data.totalTransactionAmount)}\n`;
                resultDiv.textContent += `- 总客户数: ${statsResult.data.totalCustomers}\n`;

                resultDiv.className = 'result success';

            } catch (error) {
                resultDiv.textContent += `\n❌ 测试失败: ${error.message}\n`;
                resultDiv.className = 'result error';
            }
        }

        // 页面加载时自动获取统计数据
        window.addEventListener('load', () => {
            getStatistics();
        });
    </script>
</body>
</html>
