<template>
  <div class="agent-stats">
    <div class="stats-header">
      <h2>代理人统计</h2>
      <div class="header-controls">
        <el-select v-model="timeRange" placeholder="选择时间范围" size="large">
          <el-option label="本月" value="month" />
          <el-option label="本季度" value="quarter" />
          <el-option label="本年" value="year" />
        </el-select>
      </div>
    </div>

    <div class="stats-grid">
      <!-- 推荐趋势 -->
      <div class="chart-card">
        <div class="chart-header">
          <h3>推荐趋势</h3>
        </div>
        <div class="chart-container">
          <div ref="recommendationTrendChart" class="chart"></div>
        </div>
      </div>

      <!-- Top 10 代理人 -->
      <div class="chart-card">
        <div class="chart-header">
          <h3>Top 10 代理人</h3>
          <div class="chart-controls">
            <el-select v-model="rankingTimeRange" placeholder="时间范围" size="default">
              <el-option label="本月" value="month" />
              <el-option label="本季度" value="quarter" />
              <el-option label="本年" value="year" />
            </el-select>
          </div>
        </div>
        <div class="ranking-container">
          <div class="ranking-table">
            <div class="table-header">
              <div class="header-cell rank">排名</div>
              <div class="header-cell agent">代理人</div>
              <div class="header-cell referrals sortable">
                推荐人数
                <el-icon class="sort-icon"><ArrowDown /></el-icon>
              </div>
              <div class="header-cell orders">推荐订单</div>
              <div class="header-cell rate">转化率</div>
            </div>
            <div class="table-body">
              <div 
                v-for="(agent, index) in topAgents" 
                :key="agent.id" 
                class="table-row"
                :class="{ 'top-3': index < 3 }"
              >
                <div class="cell rank">
                  <span class="rank-number" :class="{ 'top-3': index < 3 }">{{ index + 1 }}</span>
                </div>
                <div class="cell agent">
                  <div class="agent-info">
                    <div class="agent-name">{{ agent.name }}</div>
                  </div>
                </div>
                <div class="cell referrals">{{ agent.referrals }}</div>
                <div class="cell orders">{{ agent.orders }}</div>
                <div class="cell rate">{{ agent.conversionRate }}%</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue'
import * as echarts from 'echarts'
import { ArrowDown } from '@element-plus/icons-vue'

const timeRange = ref('month')
const rankingTimeRange = ref('month')

const recommendationTrendChart = ref(null)
let recommendationChartInstance = null

// Top 10 代理人数据
const topAgents = ref([
  { id: 1, name: '张晓明', referrals: 128, orders: 356, conversionRate: 2.78 },
  { id: 2, name: '李志强', referrals: 115, orders: 320, conversionRate: 2.78 },
  { id: 3, name: '王芳', referrals: 98, orders: 280, conversionRate: 2.86 },
  { id: 4, name: '赵伟', referrals: 86, orders: 245, conversionRate: 2.85 },
  { id: 5, name: '陈静', referrals: 78, orders: 210, conversionRate: 2.69 },
  { id: 6, name: '刘建国', referrals: 72, orders: 195, conversionRate: 2.71 },
  { id: 7, name: '黄小红', referrals: 65, orders: 180, conversionRate: 2.77 },
  { id: 8, name: '吴敏', referrals: 60, orders: 165, conversionRate: 2.75 },
  { id: 9, name: '孙丽', referrals: 55, orders: 150, conversionRate: 2.73 },
  { id: 10, name: '周强', referrals: 50, orders: 135, conversionRate: 2.70 }
])

// 初始化推荐趋势图
const initRecommendationTrendChart = () => {
  if (!recommendationTrendChart.value) return
  
  recommendationChartInstance = echarts.init(recommendationTrendChart.value)
  
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        label: {
          backgroundColor: '#6a7985'
        }
      }
    },
    legend: {
      data: ['推荐人数', '推荐订单数'],
      bottom: 10
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '15%',
      top: '10%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: ['1月', '2月', '3月', '4月', '5月', '6月'],
      axisLabel: {
        color: '#666'
      }
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        color: '#666'
      },
      splitLine: {
        lineStyle: {
          color: '#eee'
        }
      }
    },
    series: [
      {
        name: '推荐人数',
        type: 'line',
        stack: 'Total',
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: 'rgba(102, 126, 234, 0.8)' },
            { offset: 1, color: 'rgba(102, 126, 234, 0.1)' }
          ])
        },
        lineStyle: {
          color: '#667eea',
          width: 3
        },
        itemStyle: {
          color: '#667eea'
        },
        data: [320, 450, 580, 680, 720, 780]
      },
      {
        name: '推荐订单数',
        type: 'line',
        stack: 'Total',
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: 'rgba(245, 158, 11, 0.8)' },
            { offset: 1, color: 'rgba(245, 158, 11, 0.1)' }
          ])
        },
        lineStyle: {
          color: '#f59e0b',
          width: 3
        },
        itemStyle: {
          color: '#f59e0b'
        },
        data: [280, 380, 480, 550, 580, 620]
      }
    ]
  }
  
  recommendationChartInstance.setOption(option)
}

// 监听时间范围变化
watch(timeRange, () => {
  // 这里可以调用API获取对应时间范围的数据
  console.log('推荐趋势时间范围变化:', timeRange.value)
})

watch(rankingTimeRange, () => {
  // 这里可以调用API获取对应时间范围的数据
  console.log('排行榜时间范围变化:', rankingTimeRange.value)
})

// 窗口大小变化时重绘图表
const handleResize = () => {
  if (recommendationChartInstance) {
    recommendationChartInstance.resize()
  }
}

onMounted(() => {
  // 延迟初始化确保DOM已渲染
  setTimeout(() => {
    initRecommendationTrendChart()
  }, 100)
  
  window.addEventListener('resize', handleResize)
})

// 组件卸载时清理
import { onUnmounted } from 'vue'
onUnmounted(() => {
  if (recommendationChartInstance) {
    recommendationChartInstance.dispose()
  }
  window.removeEventListener('resize', handleResize)
})
</script>

<style scoped>
.agent-stats {
  padding: 24px;
}

.stats-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.stats-header h2 {
  margin: 0;
  color: #1f2937;
  font-size: 1.5rem;
  font-weight: 600;
}

.header-controls {
  display: flex;
  gap: 12px;
}

.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
}

.chart-card {
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  border: 1px solid #e5e7eb;
  overflow: hidden;
}

.chart-header {
  padding: 20px 24px;
  border-bottom: 1px solid #e5e7eb;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chart-header h3 {
  margin: 0;
  color: #374151;
  font-size: 1.1rem;
  font-weight: 600;
}

.chart-controls {
  display: flex;
  gap: 12px;
  align-items: center;
}

.chart-container {
  padding: 20px;
  height: 400px;
}

.chart {
  width: 100%;
  height: 100%;
}

.ranking-container {
  padding: 20px;
  height: 400px;
  overflow-y: auto;
}

.ranking-table {
  width: 100%;
}

.table-header {
  display: grid;
  grid-template-columns: 60px 1fr 100px 100px 80px;
  gap: 16px;
  padding: 12px 16px;
  background: #f9fafb;
  border-radius: 8px;
  margin-bottom: 16px;
  font-weight: 600;
  color: #374151;
  font-size: 0.9rem;
}

.header-cell {
  display: flex;
  align-items: center;
  gap: 4px;
}

.sortable {
  cursor: pointer;
  user-select: none;
}

.sort-icon {
  font-size: 12px;
  color: #9ca3af;
}

.table-body {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.table-row {
  display: grid;
  grid-template-columns: 60px 1fr 100px 100px 80px;
  gap: 16px;
  padding: 12px 16px;
  border-radius: 8px;
  transition: all 0.2s ease;
  cursor: pointer;
}

.table-row:hover {
  background: #f9fafb;
}

.table-row.top-3 {
  background: linear-gradient(135deg, #fef3c7, #fde68a);
  border: 1px solid #f59e0b;
}

.cell {
  display: flex;
  align-items: center;
  font-size: 0.9rem;
  color: #374151;
}

.rank-number {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: #e5e7eb;
  color: #6b7280;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 0.8rem;
}

.rank-number.top-3 {
  background: linear-gradient(135deg, #fbbf24, #f59e0b);
  color: white;
}

.agent-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.agent-name {
  font-weight: 500;
  color: #1f2937;
}

.referrals, .orders, .rate {
  text-align: center;
  font-weight: 500;
}

.rate {
  color: #10b981;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .chart-container, .ranking-container {
    height: 350px;
  }
}

@media (max-width: 768px) {
  .agent-stats {
    padding: 16px;
  }
  
  .stats-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }
  
  .chart-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }
  
  .chart-controls {
    width: 100%;
    justify-content: space-between;
  }
  
  .chart-container, .ranking-container {
    height: 300px;
  }
  
  .table-header, .table-row {
    grid-template-columns: 50px 1fr 80px 80px 70px;
    gap: 12px;
    padding: 10px 12px;
  }
  
  .header-cell, .cell {
    font-size: 0.8rem;
  }
}
</style>
