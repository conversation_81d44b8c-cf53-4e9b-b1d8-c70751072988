<template>
  <div class="dashboard-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1 class="page-title">智能回收管理系统</h1>
      <p class="page-subtitle">欢迎回来，{{ userName }}</p>
    </div>

    <!-- 数据统计卡片 -->
    <div class="stats-grid">
      <div class="stat-card">
        <div class="stat-icon">
          <el-icon><TrendCharts /></el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-number">1,234</div>
          <div class="stat-label">今日回收量</div>
        </div>
        <div class="stat-change positive">+12.5%</div>
      </div>

      <div class="stat-card">
        <div class="stat-icon">
          <el-icon><Money /></el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-number">¥8,567</div>
          <div class="stat-label">今日收入</div>
        </div>
        <div class="stat-change positive">+8.3%</div>
      </div>

      <div class="stat-card">
        <div class="stat-icon">
          <el-icon><User /></el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-number">89</div>
          <div class="stat-label">活跃用户</div>
        </div>
        <div class="stat-change positive">+5.2%</div>
      </div>

      <div class="stat-card">
        <div class="stat-icon">
          <el-icon><Box /></el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-number">456</div>
          <div class="stat-label">待处理订单</div>
        </div>
        <div class="stat-change negative">-2.1%</div>
      </div>

      <div class="stat-card">
        <div class="stat-icon">
          <el-icon><UserFilled /></el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-number">156</div>
          <div class="stat-label">代理人总数</div>
        </div>
        <div class="stat-change positive">+3.8%</div>
      </div>

      <div class="stat-card">
        <div class="stat-icon">
          <el-icon><Van /></el-icon>
        </div>
        <div class="stat-content">
          <div class="stat-number">89</div>
          <div class="stat-label">跑腿员总数</div>
        </div>
        <div class="stat-change positive">+6.7%</div>
      </div>
    </div>

    <!-- 图表区域 -->
    <div class="charts-section">
      <div class="chart-container">
        <div class="chart-header">
          <h3>回收量趋势</h3>
          <div class="chart-actions">
            <el-select v-model="timeRange" size="small" style="width: 120px">
              <el-option label="最近7天" value="7"></el-option>
              <el-option label="最近30天" value="30"></el-option>
              <el-option label="最近90天" value="90"></el-option>
            </el-select>
          </div>
        </div>
        <div class="chart-content" ref="chartContainer"></div>
      </div>

      <div class="chart-container">
        <div class="chart-header">
          <h3>回收类型分布</h3>
        </div>
        <div class="chart-content" ref="pieChartContainer"></div>
      </div>
    </div>

    <!-- 跑腿员统计 -->
    <div class="stats-section">
      <RunnerStats />
    </div>

    <!-- 代理人统计 -->
    <div class="stats-section">
      <AgentStats />
    </div>

    <!-- 功能模块 -->
    <div class="modules-section">
      <h2 class="section-title">功能模块</h2>
      <div class="modules-grid">
        <div class="module-card" @click="navigateToModule('order')">
          <div class="module-icon">
            <el-icon><Document /></el-icon>
          </div>
          <div class="module-content">
            <h3>订单管理</h3>
            <p>管理回收订单，跟踪处理状态</p>
          </div>
          <el-icon class="arrow-icon"><ArrowRight /></el-icon>
        </div>

        <div class="module-card" @click="navigateToModule('user')">
          <div class="module-icon">
            <el-icon><UserFilled /></el-icon>
          </div>
          <div class="module-content">
            <h3>用户管理</h3>
            <p>管理系统用户和权限设置</p>
          </div>
          <el-icon class="arrow-icon"><ArrowRight /></el-icon>
        </div>

        <div class="module-card" @click="navigateToModule('recycle')">
          <div class="module-icon">
            <el-icon><Box /></el-icon>
          </div>
          <div class="module-content">
            <h3>回收管理</h3>
            <p>管理回收物品和价格设置</p>
          </div>
          <el-icon class="arrow-icon"><ArrowRight /></el-icon>
        </div>

        <div class="module-card" @click="navigateToModule('report')">
          <div class="module-icon">
            <el-icon><PieChart /></el-icon>
          </div>
          <div class="module-content">
            <h3>数据报表</h3>
            <p>查看详细的数据统计和分析</p>
          </div>
          <el-icon class="arrow-icon"><ArrowRight /></el-icon>
        </div>
      </div>
    </div>

    <!-- 最近活动 -->
    <div class="recent-activity">
      <h2 class="section-title">最近活动</h2>
      <div class="activity-list">
        <div class="activity-item" v-for="activity in recentActivities" :key="activity.id">
          <div class="activity-icon" :class="activity.type">
            <el-icon><component :is="activity.icon" /></el-icon>
          </div>
          <div class="activity-content">
            <div class="activity-title">{{ activity.title }}</div>
            <div class="activity-desc">{{ activity.description }}</div>
            <div class="activity-time">{{ activity.time }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { 
  TrendCharts, 
  Money, 
  User, 
  Box, 
  Document, 
  UserFilled, 
  PieChart, 
  ArrowRight,
  Van
} from '@element-plus/icons-vue'
import { getUserInfo } from '@/utils/auth.js'
import * as echarts from 'echarts'
import RunnerStats from './RunnerStats.vue'
import AgentStats from './AgentStats.vue'

const router = useRouter()
const timeRange = ref('7')
const chartContainer = ref(null)
const pieChartContainer = ref(null)

// 用户信息
const userName = computed(() => {
  const userInfo = getUserInfo()
  return userInfo?.username || userInfo?.name || '用户'
})

// 最近活动数据
const recentActivities = ref([
  {
    id: 1,
    type: 'order',
    icon: 'Document',
    title: '新订单创建',
    description: '用户张三创建了新的回收订单',
    time: '2分钟前'
  },
  {
    id: 2,
    type: 'payment',
    icon: 'Money',
    title: '支付完成',
    description: '订单#12345支付完成，金额¥156.00',
    time: '15分钟前'
  },
  {
    id: 3,
    type: 'user',
    icon: 'User',
    title: '新用户注册',
    description: '新用户李四完成注册',
    time: '1小时前'
  },
  {
    id: 4,
    type: 'system',
    icon: 'Box',
    title: '系统更新',
    description: '系统完成自动更新维护',
    time: '2小时前'
  }
])

// 导航到功能模块
const navigateToModule = (module) => {
  switch (module) {
    case 'order':
      router.push('/order')
      break
    case 'user':
      router.push('/user')
      break
    case 'recycle':
      router.push('/recycle')
      break
    case 'report':
      router.push('/report')
      break
    default:
      break
  }
}

// 初始化图表
const initCharts = () => {
  // 回收量趋势图
  if (chartContainer.value) {
    const chart = echarts.init(chartContainer.value)
    const option = {
      tooltip: {
        trigger: 'axis'
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
      },
      yAxis: {
        type: 'value'
      },
      series: [
        {
          name: '回收量',
          type: 'line',
          smooth: true,
          data: [120, 132, 101, 134, 90, 230, 210],
          itemStyle: {
            color: '#667eea'
          },
          areaStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                { offset: 0, color: 'rgba(102, 126, 234, 0.3)' },
                { offset: 1, color: 'rgba(102, 126, 234, 0.1)' }
              ]
            }
          }
        }
      ]
    }
    chart.setOption(option)
  }

  // 回收类型分布图
  if (pieChartContainer.value) {
    const pieChart = echarts.init(pieChartContainer.value)
    const pieOption = {
      tooltip: {
        trigger: 'item'
      },
      legend: {
        orient: 'vertical',
        left: 'left'
      },
      series: [
        {
          name: '回收类型',
          type: 'pie',
          radius: '50%',
          data: [
            { value: 1048, name: '废纸' },
            { value: 735, name: '废金属' },
            { value: 580, name: '废塑料' },
            { value: 484, name: '废玻璃' },
            { value: 300, name: '其他' }
          ],
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }
      ]
    }
    pieChart.setOption(pieOption)
  }
}

onMounted(() => {
  // 延迟初始化图表，确保DOM已渲染
  setTimeout(() => {
    initCharts()
  }, 100)
})
</script>

<style scoped>
.dashboard-container {
  padding: 24px;
  background: #f5f7fa;
  min-height: calc(100vh - 60px);
}

.page-header {
  margin-bottom: 32px;
  text-align: center;
}

.page-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 8px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.page-subtitle {
  font-size: 1.1rem;
  color: #6b7280;
  margin: 0;
}

/* 数据统计卡片 */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 24px;
  margin-bottom: 32px;
}

/* 确保6个卡片时的布局 */
.stats-grid:has(.stat-card:nth-child(6)) {
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}

.stat-card {
  background: white;
  padding: 24px;
  border-radius: 16px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  border: 1px solid #e5e7eb;
  display: flex;
  align-items: center;
  gap: 16px;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 24px rgba(0, 0, 0, 0.1);
}

.stat-icon {
  width: 60px;
  height: 60px;
  border-radius: 16px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 24px;
}

.stat-content {
  flex: 1;
}

.stat-number {
  font-size: 2rem;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 0.9rem;
  color: #6b7280;
}

.stat-change {
  font-size: 0.9rem;
  font-weight: 600;
  padding: 4px 8px;
  border-radius: 8px;
}

.stat-change.positive {
  color: #10b981;
  background: rgba(16, 185, 129, 0.1);
}

.stat-change.negative {
  color: #ef4444;
  background: rgba(239, 68, 68, 0.1);
}

/* 图表区域 */
.charts-section {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 24px;
  margin-bottom: 32px;
}

.chart-container {
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  border: 1px solid #e5e7eb;
  overflow: hidden;
}

.chart-header {
  padding: 20px 24px;
  border-bottom: 1px solid #e5e7eb;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chart-header h3 {
  margin: 0;
  font-size: 1.2rem;
  font-weight: 600;
  color: #1f2937;
}

.chart-content {
  height: 300px;
  padding: 20px;
}

/* 功能模块 */
.modules-section {
  margin-bottom: 32px;
}

.section-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 20px;
}

.modules-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.module-card {
  background: white;
  padding: 24px;
  border-radius: 16px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  border: 1px solid #e5e7eb;
  display: flex;
  align-items: center;
  gap: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}

.module-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 24px rgba(0, 0, 0, 0.1);
  border-color: #667eea;
}

.module-icon {
  width: 56px;
  height: 56px;
  border-radius: 16px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 20px;
}

.module-content {
  flex: 1;
}

.module-content h3 {
  margin: 0 0 8px 0;
  font-size: 1.1rem;
  font-weight: 600;
  color: #1f2937;
}

.module-content p {
  margin: 0;
  font-size: 0.9rem;
  color: #6b7280;
  line-height: 1.4;
}

.arrow-icon {
  color: #9ca3af;
  font-size: 16px;
  transition: transform 0.3s ease;
}

.module-card:hover .arrow-icon {
  transform: translateX(4px);
  color: #667eea;
}

/* 最近活动 */
.recent-activity {
  margin-bottom: 32px;
}

.activity-list {
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  border: 1px solid #e5e7eb;
  overflow: hidden;
}

.activity-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 20px 24px;
  border-bottom: 1px solid #f3f4f6;
  transition: background-color 0.3s ease;
}

.activity-item:last-child {
  border-bottom: none;
}

.activity-item:hover {
  background-color: #f9fafb;
}

.activity-icon {
  width: 40px;
  height: 40px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 16px;
}

.activity-icon.order {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
}

.activity-icon.payment {
  background: linear-gradient(135deg, #10b981, #059669);
}

.activity-icon.user {
  background: linear-gradient(135deg, #f59e0b, #d97706);
}

.activity-icon.system {
  background: linear-gradient(135deg, #8b5cf6, #7c3aed);
}

.activity-content {
  flex: 1;
}

.activity-title {
  font-size: 0.95rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 4px;
}

.activity-desc {
  font-size: 0.85rem;
  color: #6b7280;
  margin-bottom: 4px;
}

.activity-time {
  font-size: 0.75rem;
  color: #9ca3af;
}

/* 统计部分样式 */
.stats-section {
  margin: 32px 0;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .charts-section {
    grid-template-columns: 1fr;
  }
  
  .chart-content {
    height: 250px;
  }
  
  .stats-section {
    margin: 24px 0;
  }
}

@media (max-width: 768px) {
  .dashboard-container {
    padding: 16px;
  }
  
  .page-title {
    font-size: 2rem;
  }
  
  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
    gap: 16px;
  }
  
  .stat-card {
    padding: 20px;
  }
  
  .stat-icon {
    width: 50px;
    height: 50px;
    font-size: 20px;
  }
  
  .stat-number {
    font-size: 1.5rem;
  }
  
  .modules-grid {
    grid-template-columns: 1fr;
  }
  
  .chart-content {
    height: 200px;
  }
}

@media (max-width: 480px) {
  .page-title {
    font-size: 1.8rem;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .stat-card {
    flex-direction: column;
    text-align: center;
    gap: 12px;
  }
  
  .chart-header {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }
}
</style>
