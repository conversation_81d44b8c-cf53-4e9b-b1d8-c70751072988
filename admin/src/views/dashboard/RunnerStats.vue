<template>
  <div class="runner-stats">
    <div class="stats-header">
      <h2>跑腿员统计</h2>
      <div class="header-controls">
        <el-select v-model="timeRange" placeholder="选择时间范围" size="large">
          <el-option label="本周" value="week" />
          <el-option label="本月" value="month" />
          <el-option label="本季度" value="quarter" />
          <el-option label="本年" value="year" />
        </el-select>
      </div>
    </div>

    <div class="stats-grid">
      <!-- 订单与重量趋势 -->
      <div class="chart-card">
        <div class="chart-header">
          <h3>订单与重量趋势</h3>
        </div>
        <div class="chart-container">
          <div ref="orderTrendChart" class="chart"></div>
        </div>
      </div>

      <!-- 品类重量占比 -->
      <div class="chart-card">
        <div class="chart-header">
          <h3>品类重量占比</h3>
          <div class="chart-controls">
            <el-select v-model="weightTimeRange" placeholder="时间范围" size="default">
              <el-option label="本月" value="month" />
              <el-option label="本季度" value="quarter" />
              <el-option label="本年" value="year" />
            </el-select>
            <el-button type="primary" size="default">导出报表</el-button>
          </div>
        </div>
        <div class="chart-container">
          <div ref="weightProportionChart" class="chart"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue'
import * as echarts from 'echarts'

const timeRange = ref('week')
const weightTimeRange = ref('month')

const orderTrendChart = ref(null)
const weightProportionChart = ref(null)

let orderChartInstance = null
let weightChartInstance = null

// 初始化订单趋势图
const initOrderTrendChart = () => {
  if (!orderTrendChart.value) return
  
  orderChartInstance = echarts.init(orderTrendChart.value)
  
  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    legend: {
      data: ['订单数量'],
      bottom: 10
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '15%',
      top: '10%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: ['1日', '2日', '3日', '4日', '5日', '6日', '7日'],
      axisLabel: {
        color: '#666'
      }
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        color: '#666'
      },
      splitLine: {
        lineStyle: {
          color: '#eee'
        }
      }
    },
    series: [
      {
        name: '订单数量',
        type: 'bar',
        data: [320, 450, 380, 500, 450, 550, 520],
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: '#667eea' },
            { offset: 1, color: '#764ba2' }
          ])
        },
        barWidth: '60%',
        emphasis: {
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: '#5a6fd8' },
              { offset: 1, color: '#6a4190' }
            ])
          }
        }
      }
    ]
  }
  
  orderChartInstance.setOption(option)
}

// 初始化重量占比图
const initWeightProportionChart = () => {
  if (!weightProportionChart.value) return
  
  weightChartInstance = echarts.init(weightProportionChart.value)
  
  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      left: 'left',
      top: 'middle',
      textStyle: {
        color: '#666'
      }
    },
    series: [
      {
        name: '品类重量',
        type: 'pie',
        radius: ['40%', '70%'],
        center: ['60%', '50%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 10,
          borderColor: '#fff',
          borderWidth: 2
        },
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: '18',
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: [
          { value: 35, name: '食品', itemStyle: { color: '#667eea' } },
          { value: 25, name: '日用品', itemStyle: { color: '#10b981' } },
          { value: 20, name: '电子产品', itemStyle: { color: '#f59e0b' } },
          { value: 15, name: '文件', itemStyle: { color: '#8b5cf6' } },
          { value: 5, name: '其他', itemStyle: { color: '#6b7280' } }
        ]
      }
    ]
  }
  
  weightChartInstance.setOption(option)
}

// 监听时间范围变化
watch(timeRange, () => {
  // 这里可以调用API获取对应时间范围的数据
  console.log('时间范围变化:', timeRange.value)
})

watch(weightTimeRange, () => {
  // 这里可以调用API获取对应时间范围的数据
  console.log('重量占比时间范围变化:', weightTimeRange.value)
})

// 窗口大小变化时重绘图表
const handleResize = () => {
  if (orderChartInstance) {
    orderChartInstance.resize()
  }
  if (weightChartInstance) {
    weightChartInstance.resize()
  }
}

onMounted(() => {
  // 延迟初始化确保DOM已渲染
  setTimeout(() => {
    initOrderTrendChart()
    initWeightProportionChart()
  }, 100)
  
  window.addEventListener('resize', handleResize)
})

// 组件卸载时清理
import { onUnmounted } from 'vue'
onUnmounted(() => {
  if (orderChartInstance) {
    orderChartInstance.dispose()
  }
  if (weightChartInstance) {
    weightChartInstance.dispose()
  }
  window.removeEventListener('resize', handleResize)
})
</script>

<style scoped>
.runner-stats {
  padding: 24px;
}

.stats-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.stats-header h2 {
  margin: 0;
  color: #1f2937;
  font-size: 1.5rem;
  font-weight: 600;
}

.header-controls {
  display: flex;
  gap: 12px;
}

.stats-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
}

.chart-card {
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  border: 1px solid #e5e7eb;
  overflow: hidden;
}

.chart-header {
  padding: 20px 24px;
  border-bottom: 1px solid #e5e7eb;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chart-header h3 {
  margin: 0;
  color: #374151;
  font-size: 1.1rem;
  font-weight: 600;
}

.chart-controls {
  display: flex;
  gap: 12px;
  align-items: center;
}

.chart-container {
  padding: 20px;
  height: 400px;
}

.chart {
  width: 100%;
  height: 100%;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .chart-container {
    height: 350px;
  }
}

@media (max-width: 768px) {
  .runner-stats {
    padding: 16px;
  }
  
  .stats-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }
  
  .chart-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }
  
  .chart-controls {
    width: 100%;
    justify-content: space-between;
  }
  
  .chart-container {
    height: 300px;
  }
}
</style>
