<template>
  <div class="welcome-container">
    <!-- 欢迎标题 -->
    <div class="welcome-header">
      <h1>欢迎回来！</h1>
      <p>{{ getCurrentTime() }}</p>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-grid">
      <el-card class="stat-card today-orders" shadow="hover">
        <div class="stat-content">
          <div class="stat-icon">
            <el-icon size="40"><Document /></el-icon>
          </div>
          <div class="stat-info">
            <h3>{{ statistics.todayOrderCount || 0 }}</h3>
            <p>今日订单</p>
            <div class="stat-total">
              <span class="total-label">总订单:</span>
              <span class="total-value">{{ statistics.totalOrderCount || 0 }}</span>
            </div>
          </div>
        </div>
      </el-card>

      <el-card class="stat-card today-amount" shadow="hover">
        <div class="stat-content">
          <div class="stat-icon">
            <el-icon size="40"><Money /></el-icon>
          </div>
          <div class="stat-info">
            <h3>¥{{ formatAmount(statistics.todayTransactionAmount) }}</h3>
            <p>今日交易额</p>
            <div class="stat-total">
              <span class="total-label">总交易额:</span>
              <span class="total-value">¥{{ formatAmount(statistics.totalTransactionAmount) }}</span>
            </div>
          </div>
        </div>
      </el-card>

      <el-card class="stat-card today-customers" shadow="hover">
        <div class="stat-content">
          <div class="stat-icon">
            <el-icon size="40"><User /></el-icon>
          </div>
          <div class="stat-info">
            <h3>{{ statistics.todayNewCustomers || 0 }}</h3>
            <p>今日新增客户</p>
            <div class="stat-total">
              <span class="total-label">总客户:</span>
              <span class="total-value">{{ statistics.totalCustomers || 0 }}</span>
            </div>
          </div>
        </div>
      </el-card>

      <el-card class="stat-card total-orders" shadow="hover">
        <div class="stat-content">
          <div class="stat-icon">
            <el-icon size="40"><DataBoard /></el-icon>
          </div>
          <div class="stat-info">
            <h3>{{ statistics.totalOrderCount || 0 }}</h3>
            <p>总订单数</p>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 趋势图表 -->
    <div class="charts-container">
      <!-- 时间周期选择 -->
      <div class="chart-header">
        <h2>数据趋势</h2>
        <el-radio-group v-model="selectedPeriod" @change="loadTrendData">
          <el-radio-button value="week">最近一周</el-radio-button>
          <el-radio-button value="month">最近一月</el-radio-button>
          <el-radio-button value="year">最近一年</el-radio-button>
        </el-radio-group>
      </div>

      <!-- 图表网格 -->
      <div class="charts-grid">
        <!-- 订单量趋势 -->
        <el-card class="chart-card" shadow="never">
          <template #header>
            <div class="chart-title">
              <el-icon><TrendCharts /></el-icon>
              <span>订单量趋势</span>
            </div>
          </template>
          <div ref="orderChartRef" class="chart-container"></div>
        </el-card>

        <!-- 交易额趋势 -->
        <el-card class="chart-card" shadow="never">
          <template #header>
            <div class="chart-title">
              <el-icon><Money /></el-icon>
              <span>交易额趋势</span>
            </div>
          </template>
          <div ref="amountChartRef" class="chart-container"></div>
        </el-card>

        <!-- 新增客户趋势 -->
        <el-card class="chart-card" shadow="never">
          <template #header>
            <div class="chart-title">
              <el-icon><UserFilled /></el-icon>
              <span>新增客户趋势</span>
            </div>
          </template>
          <div ref="customerChartRef" class="chart-container"></div>
        </el-card>
      </div>
    </div>

    <!-- 快捷操作 -->
    <div class="quick-actions">
      <h2>快捷操作</h2>
      <div class="actions-grid">
        <el-card class="action-card" shadow="hover" @click="goToOrders">
          <div class="action-content">
            <el-icon size="30"><List /></el-icon>
            <span>订单管理</span>
          </div>
        </el-card>

        <el-card class="action-card" shadow="hover" @click="goToUsers">
          <div class="action-content">
            <el-icon size="30"><User /></el-icon>
            <span>用户管理</span>
          </div>
        </el-card>

        <el-card class="action-card" shadow="hover" @click="goToPrices">
          <div class="action-content">
            <el-icon size="30"><PriceTag /></el-icon>
            <span>价格管理</span>
          </div>
        </el-card>

        <el-card class="action-card" shadow="hover" @click="goToDict">
          <div class="action-content">
            <el-icon size="30"><Setting /></el-icon>
            <span>数据字典</span>
          </div>
        </el-card>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import {
  Document,
  Money,
  User,
  DataBoard,
  TrendCharts,
  UserFilled,
  List,
  PriceTag,
  Setting
} from '@element-plus/icons-vue'
import * as echarts from 'echarts'
import { dashboardApi } from '@/api/dashboard'

const router = useRouter()

// 响应式数据
const statistics = reactive({
  todayOrderCount: 0,
  todayTransactionAmount: 0,
  todayNewCustomers: 0,
  totalOrderCount: 0,
  totalTransactionAmount: 0,
  totalCustomers: 0
})

const selectedPeriod = ref('week')
const trendData = reactive({
  dates: [],
  orderCounts: [],
  transactionAmounts: [],
  newCustomers: []
})

// 图表引用
const orderChartRef = ref()
const amountChartRef = ref()
const customerChartRef = ref()

// 图表实例
let orderChart = null
let amountChart = null
let customerChart = null

// 页面加载时初始化
onMounted(async () => {
  await loadStatistics()
  await loadTrendData()

  nextTick(() => {
    initCharts()
  })
})

// 加载统计数据
const loadStatistics = async () => {
  try {
    const response = await dashboardApi.getStatistics()
    if (response.success) {
      Object.assign(statistics, response.data)
    } else {
      ElMessage.error('加载统计数据失败')
    }
  } catch (error) {
    console.error('加载统计数据失败:', error)
    ElMessage.error('加载统计数据失败')
  }
}

// 加载趋势数据
const loadTrendData = async () => {
  try {
    const response = await dashboardApi.getTrendData(selectedPeriod.value)
    if (response.success) {
      Object.assign(trendData, response.data)
      updateCharts()
    } else {
      ElMessage.error('加载趋势数据失败')
    }
  } catch (error) {
    console.error('加载趋势数据失败:', error)
    ElMessage.error('加载趋势数据失败')
  }
}

// 初始化图表
const initCharts = () => {
  // 订单量趋势图
  orderChart = echarts.init(orderChartRef.value)

  // 交易额趋势图
  amountChart = echarts.init(amountChartRef.value)

  // 新增客户趋势图
  customerChart = echarts.init(customerChartRef.value)

  updateCharts()
}

// 更新图表
const updateCharts = () => {
  if (!orderChart || !amountChart || !customerChart) return

  const commonOption = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: trendData.dates
    },
    yAxis: {
      type: 'value'
    }
  }

  // 订单量趋势
  orderChart.setOption({
    ...commonOption,
    series: [{
      name: '订单量',
      type: 'line',
      smooth: true,
      data: trendData.orderCounts,
      itemStyle: {
        color: '#409EFF'
      },
      areaStyle: {
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [{
            offset: 0, color: 'rgba(64, 158, 255, 0.3)'
          }, {
            offset: 1, color: 'rgba(64, 158, 255, 0.1)'
          }]
        }
      }
    }]
  })

  // 交易额趋势
  amountChart.setOption({
    ...commonOption,
    series: [{
      name: '交易额',
      type: 'line',
      smooth: true,
      data: trendData.transactionAmounts,
      itemStyle: {
        color: '#67C23A'
      },
      areaStyle: {
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [{
            offset: 0, color: 'rgba(103, 194, 58, 0.3)'
          }, {
            offset: 1, color: 'rgba(103, 194, 58, 0.1)'
          }]
        }
      }
    }]
  })

  // 新增客户趋势
  customerChart.setOption({
    ...commonOption,
    series: [{
      name: '新增客户',
      type: 'line',
      smooth: true,
      data: trendData.newCustomers,
      itemStyle: {
        color: '#E6A23C'
      },
      areaStyle: {
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [{
            offset: 0, color: 'rgba(230, 162, 60, 0.3)'
          }, {
            offset: 1, color: 'rgba(230, 162, 60, 0.1)'
          }]
        }
      }
    }]
  })
}

// 格式化金额
const formatAmount = (amount) => {
  if (!amount) return '0.00'
  return Number(amount).toLocaleString('zh-CN', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  })
}

// 获取当前时间
const getCurrentTime = () => {
  const now = new Date()
  const options = {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    weekday: 'long',
    hour: '2-digit',
    minute: '2-digit'
  }
  return now.toLocaleDateString('zh-CN', options)
}

// 快捷操作导航
const goToOrders = () => {
  router.push('/recycle/order')
}

const goToUsers = () => {
  router.push('/system/user')
}

const goToPrices = () => {
  router.push('/recycle/price')
}

const goToDict = () => {
  router.push('/system/dict')
}
</script>

<style scoped>
.welcome-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: calc(100vh - 60px);
}

.welcome-header {
  text-align: center;
  margin-bottom: 30px;
}

.welcome-header h1 {
  font-size: 32px;
  color: #303133;
  margin: 0 0 10px 0;
  font-weight: 600;
}

.welcome-header p {
  font-size: 16px;
  color: #909399;
  margin: 0;
}

/* 统计卡片样式 */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.stat-card {
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-5px);
}

.stat-content {
  display: flex;
  align-items: center;
  padding: 10px;
}

.stat-icon {
  margin-right: 20px;
  padding: 15px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.stat-info h3 {
  font-size: 28px;
  font-weight: 700;
  margin: 0 0 5px 0;
  color: #303133;
}

.stat-info p {
  font-size: 14px;
  color: #909399;
  margin: 0;
}

/* 不同卡片的主题色 */
.today-orders .stat-icon {
  background-color: rgba(64, 158, 255, 0.1);
  color: #409EFF;
}

.today-amount .stat-icon {
  background-color: rgba(103, 194, 58, 0.1);
  color: #67C23A;
}

.today-customers .stat-icon {
  background-color: rgba(230, 162, 60, 0.1);
  color: #E6A23C;
}

.total-orders .stat-icon {
  background-color: rgba(245, 108, 108, 0.1);
  color: #F56C6C;
}

/* 图表容器样式 */
.charts-container {
  margin-bottom: 30px;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.chart-header h2 {
  font-size: 24px;
  color: #303133;
  margin: 0;
  font-weight: 600;
}

.charts-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 20px;
}

.chart-card {
  border-radius: 12px;
  overflow: hidden;
}

.chart-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  color: #303133;
}

.chart-container {
  height: 300px;
  width: 100%;
}

/* 快捷操作样式 */
.quick-actions {
  margin-bottom: 30px;
}

.quick-actions h2 {
  font-size: 24px;
  color: #303133;
  margin: 0 0 20px 0;
  font-weight: 600;
}

.actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.action-card {
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.action-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.action-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px;
  gap: 10px;
}

.action-content span {
  font-size: 16px;
  font-weight: 500;
  color: #303133;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .welcome-container {
    padding: 10px;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .charts-grid {
    grid-template-columns: 1fr;
  }

  .chart-header {
    flex-direction: column;
    gap: 15px;
    align-items: flex-start;
  }

  .actions-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .welcome-header h1 {
    font-size: 24px;
  }
}

/* 卡片动画 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.stat-card,
.chart-card,
.action-card {
  animation: fadeInUp 0.6s ease-out;
}

/* Element Plus 样式覆盖 */
:deep(.el-card__body) {
  padding: 0px 20px;
}

:deep(.el-card__header) {
  padding: 15px 20px;
  border-bottom: 1px solid #ebeef5;
  background-color: #fafafa;
}

:deep(.el-radio-button__inner) {
  border-radius: 6px;
  margin: 0 2px;
}

/* 响应式设计 */
@media (max-width: 768px) {

  .chart-container {
    height: 200px;
    width: 100%;
  }

}
</style>
