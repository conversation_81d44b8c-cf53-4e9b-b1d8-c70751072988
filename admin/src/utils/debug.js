/**
 * 调试工具函数
 */

/**
 * 检查后端服务连接状态
 */
export async function checkBackendConnection() {
  try {
    console.log('🔍 检查后端服务连接...')
    
    // 检查验证码生成接口
    const captchaResponse = await fetch('/api/captcha/generate')
    console.log('验证码接口状态:', captchaResponse.status)
    
    if (captchaResponse.ok) {
      const captchaResult = await captchaResponse.json()
      console.log('验证码接口正常:', captchaResult.success)
      return true
    } else {
      console.error('验证码接口异常:', captchaResponse.status, captchaResponse.statusText)
      return false
    }
  } catch (error) {
    console.error('后端服务连接失败:', error.message)
    console.log('请确保后端服务已启动在 http://localhost:8081')
    return false
  }
}

/**
 * 测试API接口
 */
export async function testApiEndpoints() {
  console.log('🧪 开始测试API接口...')
  
  const endpoints = [
    { name: '验证码生成', url: '/api/captcha/generate', method: 'GET' },
    { name: 'Swagger文档', url: '/swagger-ui.html', method: 'GET' }
  ]
  
  for (const endpoint of endpoints) {
    try {
      const response = await fetch(endpoint.url, { method: endpoint.method })
      console.log(`${endpoint.name}: ${response.status} ${response.statusText}`)
    } catch (error) {
      console.error(`${endpoint.name}: 连接失败 -`, error.message)
    }
  }
}

/**
 * 打印环境信息
 */
export function printEnvironmentInfo() {
  console.log('🌍 环境信息:')
  console.log('- 模式:', import.meta.env.MODE)
  console.log('- 开发环境:', import.meta.env.DEV)
  console.log('- 生产环境:', import.meta.env.PROD)
  console.log('- API基础URL:', import.meta.env.VITE_API_BASE_URL || '使用代理')
  console.log('- 后端URL:', import.meta.env.VITE_BACKEND_URL || 'http://localhost:8081')
}

/**
 * 调试模式检查
 */
export function enableDebugMode() {
  if (import.meta.env.DEV) {
    // 在开发环境中启用调试
    window.debugAPI = {
      checkConnection: checkBackendConnection,
      testEndpoints: testApiEndpoints,
      printEnv: printEnvironmentInfo
    }
    
    console.log('🔧 调试模式已启用')
    console.log('可用命令:')
    console.log('- debugAPI.checkConnection() - 检查后端连接')
    console.log('- debugAPI.testEndpoints() - 测试API接口')
    console.log('- debugAPI.printEnv() - 打印环境信息')
  }
}
