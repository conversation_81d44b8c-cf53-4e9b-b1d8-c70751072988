<template>
  <div class="dashboard">
    <el-container>
      <el-header class="header">
        <div class="header-left">
          <h2>叮咚回收管理系统</h2>
        </div>
        <div class="header-right">
          <el-dropdown @command="handleCommand">
            <span class="user-info">
              <el-icon><User /></el-icon>
              {{ userInfo?.nickName || userInfo?.userNo || '用户' }}
              <el-icon class="el-icon--right"><arrow-down /></el-icon>
            </span>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="profile">个人信息</el-dropdown-item>
                <el-dropdown-item command="logout" divided>退出登录</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </el-header>

      <el-main class="main">
        <div class="welcome-card">
          <el-card>
            <template #header>
              <div class="card-header">
                <span>欢迎使用叮咚回收管理系统</span>
              </div>
            </template>

            <div class="welcome-content">
              <p>登录成功！您现在可以使用系统的各项功能。</p>

              <div class="user-details" v-if="userInfo">
                <h4>用户信息：</h4>
                <el-descriptions :column="2" border>
                  <el-descriptions-item label="用户名">
                    {{ userInfo.userNo }}
                  </el-descriptions-item>
                  <el-descriptions-item label="姓名">
                    {{ userInfo.nickName || '未设置' }}
                  </el-descriptions-item>
                  <el-descriptions-item label="用户组">
                    {{ userInfo.groupId || '默认组' }}
                  </el-descriptions-item>
                  <el-descriptions-item label="状态">
                    <el-tag :type="userInfo.state === '1' ? 'success' : 'danger'">
                      {{ userInfo.state === '1' ? '正常' : '禁用' }}
                    </el-tag>
                  </el-descriptions-item>
                </el-descriptions>
              </div>

              <div class="token-info">
                <h4>Token信息：</h4>
                <el-input
                  v-model="tokenDisplay"
                  type="textarea"
                  :rows="3"
                  readonly
                  placeholder="Token信息"
                />
              </div>

              <div class="actions">
                <el-button type="primary" @click="testApi">测试API</el-button>
                <el-button type="warning" @click="refreshToken">刷新Token</el-button>
                <el-button type="danger" @click="logout">退出登录</el-button>
              </div>
            </div>
          </el-card>
        </div>
      </el-main>
    </el-container>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { User, ArrowDown } from '@element-plus/icons-vue'
import { getUserInfo, getToken, removeToken } from '../utils/auth.js'

// 响应式数据
const userInfo = ref(null)
const token = ref('')

// 计算属性
const tokenDisplay = computed(() => {
  if (!token.value) return ''
  return `${token.value.substring(0, 50)}...`
})

// 处理下拉菜单命令
const handleCommand = (command) => {
  switch (command) {
    case 'profile':
      ElMessage.info('个人信息功能开发中...')
      break
    case 'logout':
      logout()
      break
  }
}

// 测试API
const testApi = async () => {
  try {
    // 这里可以调用一些测试API
    ElMessage.success('API测试成功！')
  } catch (error) {
    ElMessage.error('API测试失败: ' + error.message)
  }
}

// 刷新Token
const refreshToken = () => {
  ElMessage.info('刷新Token功能开发中...')
}

// 退出登录
const logout = async () => {
  try {
    await ElMessageBox.confirm('确定要退出登录吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    removeToken()
    ElMessage.success('已退出登录')

    // 重新加载页面回到登录页
    window.location.reload()
  } catch {
    // 用户取消
  }
}

// 组件挂载时初始化
onMounted(() => {
  userInfo.value = getUserInfo()
  token.value = getToken()

  if (!userInfo.value || !token.value) {
    ElMessage.warning('未找到登录信息，请重新登录')
    removeToken()
    window.location.reload()
  }
})
</script>

<style scoped>
.dashboard {
  height: 100vh;
}

.header {
  background-color: #409eff;
  color: white;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
}

.header-left h2 {
  margin: 0;
  font-size: 20px;
}

.header-right {
  display: flex;
  align-items: center;
}

.user-info {
  display: flex;
  align-items: center;
  cursor: pointer;
  padding: 8px 12px;
  border-radius: 4px;
  transition: background-color 0.3s;
}

.user-info:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.user-info .el-icon {
  margin-right: 4px;
}

.main {
  background-color: #f5f5f5;
  padding: 20px;
}

.welcome-card {
  max-width: 800px;
  margin: 0 auto;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 18px;
  font-weight: bold;
}

.welcome-content {
  line-height: 1.6;
}

.welcome-content p {
  font-size: 16px;
  color: #666;
  margin-bottom: 20px;
}

.user-details,
.token-info {
  margin: 20px 0;
}

.user-details h4,
.token-info h4 {
  margin-bottom: 12px;
  color: #333;
}

.actions {
  margin-top: 20px;
  text-align: center;
}

.actions .el-button {
  margin: 0 8px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header {
    padding: 0 10px;
  }

  .header-left h2 {
    font-size: 16px;
  }

  .main {
    padding: 10px;
  }

  .actions .el-button {
    margin: 4px;
    width: 100px;
  }
}
</style>
