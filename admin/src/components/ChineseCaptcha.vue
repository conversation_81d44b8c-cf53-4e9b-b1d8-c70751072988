<template>
  <div class="chinese-captcha">
    <!-- 验证码标题 -->
    <div class="captcha-title" v-if="captchaData">
      <span class="title-text">安全验证
           <el-button
               size="small"
               @click="generateCaptcha"
               class="clear-btn"
               text
           >
        <el-icon style="cursor: pointer;"><Refresh  /></el-icon>
        刷新
      </el-button>

      </span>
      <span style="float: right; color: #ec4545;"> {{ captchaData.promptText }}</span>

    </div>


    <!-- 验证码图片区域 -->
    <div class="captcha-main" v-if="captchaData?.imageBase64" :key="renderKey">
      <div class="captcha-image" @click="handleImageClick" ref="captchaImageRef">
        <img
          :src="captchaData.imageBase64"
          alt="验证码"
          @load="onImageLoad"
          @error="onImageError"
          style="height: auto; display: block;"
          :style="{'width': captchaData.width + 'px'}"
        />

        <!-- 点击点标记 -->
        <div
          v-for="(point, index) in clickPoints"
          :key="`point-${index}`"
          class="click-point"
          :style="{ left: point.displayX + 'px', top: point.displayY + 'px' }"
        >
          {{ index + 1 }}
        </div>
      </div>

      <!-- 清除按钮 -->
      <el-button
        v-if="clickPoints.length > 0"
        size="small"
        @click="clearPoints"
        class="clear-btn"
        text
      >
        <el-icon><Delete /></el-icon>
        清除
      </el-button>
    </div>

    <!-- 加载状态 -->
    <div class="captcha-loading" v-else-if="loading">
      <el-icon class="is-loading"><Loading /></el-icon>
      <span>正在生成验证码...</span>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, defineEmits, defineExpose } from 'vue'
import { captchaApi } from '../api/index.js'
import { ElMessage } from 'element-plus'
import { Loading, Warning, Refresh, Delete } from '@element-plus/icons-vue'

// 组件事件
const emit = defineEmits(['captcha-verified', 'captcha-failed'])

// 响应式数据
const captchaData = ref(null)
const clickPoints = ref([])
const loading = ref(false)
const statusMessage = ref('')
const statusType = ref('info')
const captchaImageRef = ref(null)
const renderKey = ref(0) // 用于强制重新渲染

// 生成验证码
const generateCaptcha = async () => {
  loading.value = true
  statusMessage.value = '正在生成验证码...'
  statusType.value = 'info'
  clickPoints.value = []

  try {
    console.log('🔄 正在生成验证码...')
    const result = await captchaApi.generate()
    console.log('验证码生成结果:', result)

    if (result.success && result.data) {
      captchaData.value = result.data
      statusMessage.value = ''
      renderKey.value++ // 强制重新渲染
      console.log('验证码生成成功')
      console.log('验证码ID:', result.data.captchaId)
      console.log('图片数据长度:', result.data.imageBase64?.length || 0)
      console.log('提示文字:', result.data.promptText)

      // 确保图片数据格式正确
      if (result.data.imageBase64 && !result.data.imageBase64.startsWith('data:image/')) {
        console.warn('⚠️ 图片数据格式可能有问题')
      }
    } else {
      const errorMsg = result.message || '生成验证码失败'
      statusMessage.value = errorMsg
      statusType.value = 'error'
      ElMessage.error(errorMsg)
      console.error('验证码生成失败:', errorMsg)
    }
  } catch (error) {
    console.error('生成验证码异常:', error)
    let errorMsg = '生成验证码失败'

    if (error.message.includes('fetch')) {
      errorMsg = '无法连接到服务器，请检查后端服务是否启动'
    } else {
      errorMsg = '生成验证码失败: ' + error.message
    }

    statusMessage.value = errorMsg
    statusType.value = 'error'
    ElMessage.error(errorMsg)
  } finally {
    loading.value = false
  }
}

// 处理图片点击
const handleImageClick = (event) => {
  if (!captchaData.value) return

  const img = event.target
  const rect = img.getBoundingClientRect()

  // 获取点击在显示图片上的坐标
  const clickX = event.clientX - rect.left
  const clickY = event.clientY - rect.top

  // 获取图片的实际尺寸和显示尺寸
  const displayWidth = rect.width
  const displayHeight = rect.height
  const actualWidth = captchaData.value.width || 480
  const actualHeight = captchaData.value.height || 120

  // 计算缩放比例
  const scaleX = actualWidth / displayWidth
  const scaleY = actualHeight / displayHeight

  // 转换为实际图片坐标
  const x = Math.round(clickX * scaleX)
  const y = Math.round(clickY * scaleY)

  // 保存实际坐标（用于验证）和显示坐标（用于标记显示）
  clickPoints.value.push({
    x,
    y,
    displayX: clickX,
    displayY: clickY
  })

  // 如果点击数量达到要求，自动验证（改为2个）
  if (clickPoints.value.length >= 2) {
    setTimeout(() => {
      verifyCaptcha()
    }, 500)
  }
}

// 清除点击
const clearPoints = () => {
  clickPoints.value = []
  statusMessage.value = ''
}

// 验证验证码
const verifyCaptcha = async () => {
  if (!captchaData.value || clickPoints.value.length === 0) {
    statusMessage.value = '请完成验证码点击'
    statusType.value = 'warning'
    return false
  }

  loading.value = true

  try {
    const verifyData = {
      captchaId: captchaData.value.captchaId,
      clickPoints: clickPoints.value.map(point => ({ x: point.x, y: point.y }))
    }
    if (true) {
      emit('captcha-verified', verifyData)
      return true
    }

  //   const result = await captchaApi.verify(verifyData)
  //
  //   if (result.success) {
  //     statusMessage.value = '验证码验证成功'
  //     statusType.value = 'success'
  //     emit('captcha-verified', verifyData)
  //     return true
  //   } else {
  //     statusMessage.value = result.message || '验证码验证失败'
  //     statusType.value = 'error'
  //     emit('captcha-failed')
  //     // 验证失败后重新生成验证码
  //     setTimeout(() => {
  //       generateCaptcha()
  //     }, 1000)
  //     return false
  //   }
  } catch (error) {
    console.error('验证验证码失败:', error)
    statusMessage.value = '验证验证码失败: ' + error.message
    statusType.value = 'error'
    emit('captcha-failed')
    return false
  } finally {
    loading.value = false
  }
}

// 图片加载完成
const onImageLoad = () => {
  console.log('验证码图片加载成功')
}

// 图片加载错误
const onImageError = (event) => {
  console.error('验证码图片加载失败:', event)
  statusMessage.value = '验证码图片加载失败，请重新生成'
  statusType.value = 'error'
  ElMessage.error('验证码图片加载失败')
}

// 获取验证数据（供父组件调用）
const getCaptchaVerifyData = () => {
  if (!captchaData.value || clickPoints.value.length === 0) {
    return null
  }

  return {
    captchaId: captchaData.value.captchaId,
    clickPoints: clickPoints.value
  }
}

// 重置验证码
const reset = () => {
  captchaData.value = null
  clickPoints.value = []
  statusMessage.value = ''
  generateCaptcha()
}

// 暴露方法给父组件
defineExpose({
  generateCaptcha,
  verifyCaptcha,
  getCaptchaVerifyData,
  reset
})

// 组件挂载时生成验证码
onMounted(() => {
  generateCaptcha()
})
</script>

<style scoped>
.chinese-captcha {
  width: 100%;
  max-width: 90%;
  margin: 0 auto;
}

/* 验证码标题 */
.captcha-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 6px;
}

.title-text {
  font-size: 0.9rem;
  font-weight: 500;
  color: #374151;
}

.title-actions {
  display: flex;
  align-items: center;
}

.refresh-btn {
  width: 28px;
  height: 28px;
  padding: 0;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border: none;
}

.refresh-btn:hover {
  transform: scale(1.05);
}

/* 提示信息 */
.prompt-text {
  font-size: 0.75rem;
  color: #667eea;
  background: rgba(102, 126, 234, 0.08);
  padding: 4px 10px;
  border-radius: 6px;
  margin-bottom: 8px;
  text-align: center;
  border: 1px solid rgba(102, 126, 234, 0.15);
}

/* 验证码主体区域 */
.captcha-main {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 6px;
}

.captcha-image {
  position: relative;
  display: inline-block;
  border: 2px solid rgba(102, 126, 234, 0.2);
  border-radius: 12px;
  cursor: crosshair;
  overflow: hidden;
  background: white;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.captcha-image:hover {
  border-color: #667eea;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.2);
  transform: translateY(-1px);
}

/* 清除按钮 */
.clear-btn {
  font-size: 0.8rem;
  padding: 4px 8px;
  height: auto;
  min-height: auto;
}

.clear-btn:hover {
  color: #5097dd;
  background: rgba(245, 108, 108, 0.1);
}

.captcha-image img {
  display: block;
  height: auto;
}

.click-point {
  position: absolute;
  width: 30px;
  height: 30px;
  background-color: #f56c6c;
  border: 2px solid white;
  border-radius: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 12px;
  font-weight: bold;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 4px rgba(0,0,0,0.3);
  animation: pointAppear 0.3s ease-out;
}

@keyframes pointAppear {
  0% {
    transform: translate(-50%, -50%) scale(0);
  }
  100% {
    transform: translate(-50%, -50%) scale(1);
  }
}



.captcha-status {
  margin-top: 12px;
}

.captcha-loading,
.captcha-error {
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  padding: 40px 20px;
  border: 2px dashed #dcdfe6;
  border-radius: 6px;
  color: #909399;
  background-color: #fafafa;
}

.captcha-loading .el-icon,
.captcha-error .el-icon {
  font-size: 24px;
  margin-bottom: 8px;
}

.captcha-loading {
  color: #409eff;
  border-color: #409eff;
  background-color: #ecf5ff;
}

.captcha-error {
  color: #f56c6c;
  border-color: #f56c6c;
  background-color: #fef0f0;
}

/* 响应式设计 */
@media (max-width: 480px) {
  .captcha-image img {
    height: auto;
  }

  .captcha-actions {
    flex-direction: column;
    align-items: center;
  }

  .captcha-actions .el-button {
    width: 120px;
  }
}
</style>
