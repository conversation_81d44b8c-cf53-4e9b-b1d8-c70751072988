<template>
  <div class="simple-captcha">
    <h3>简化验证码测试</h3>

    <div class="test-buttons">
      <button @click="testDirectAPI">测试后端直连</button>
      <button @click="testProxyAPI">测试前端代理</button>
      <button @click="generateCaptcha">生成验证码</button>
    </div>

    <div class="status" v-if="status">
      <div :class="['message', statusType]">{{ status }}</div>
    </div>

    <div class="captcha-display" v-if="captchaData">
      <div class="info">
        <p><strong>验证码ID:</strong> {{ captchaData.captchaId }}</p>
        <p><strong>提示文字:</strong> {{ captchaData.promptText }}</p>
        <p><strong>图片尺寸:</strong> {{ captchaData.width }}x{{ captchaData.height }}</p>
        <p><strong>有效期:</strong> {{ captchaData.expireTime }}秒</p>
        <p><strong>图片数据长度:</strong> {{ captchaData.imageBase64?.length || 0 }}</p>
      </div>

      <div class="image-container">
        <img
          v-if="captchaData.imageBase64"
          :src="captchaData.imageBase64"
          alt="验证码"
          @load="onImageLoad"
          @error="onImageError"
          style="max-width: 100%; border: 1px solid #ddd; border-radius: 4px;"
        />
        <div v-else class="no-image">没有图片数据</div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue'

const status = ref('')
const statusType = ref('info')
const captchaData = ref(null)

const setStatus = (message, type = 'info') => {
  status.value = message
  statusType.value = type
  console.log(`[${type.toUpperCase()}] ${message}`)
}

const testDirectAPI = async () => {
  setStatus('正在测试后端直连...', 'info')

  try {
    const response = await fetch('http://localhost:8081/api/captcha/generate')
    const result = await response.json()

    if (response.ok && result.success) {
      setStatus('后端直连成功', 'success')
      console.log('后端直连结果:', result)
    } else {
      setStatus(`后端直连失败: ${result.message || '未知错误'}`, 'error')
    }
  } catch (error) {
    setStatus(`后端直连异常: ${error.message}`, 'error')
  }
}

const testProxyAPI = async () => {
  setStatus('正在测试前端代理...', 'info')

  try {
    const response = await fetch('/api/captcha/generate')
    const result = await response.json()

    if (response.ok && result.success) {
      setStatus('前端代理成功', 'success')
      console.log('前端代理结果:', result)
    } else {
      setStatus(`前端代理失败: ${result.message || '未知错误'}`, 'error')
    }
  } catch (error) {
    setStatus(`前端代理异常: ${error.message}`, 'error')
  }
}

const generateCaptcha = async () => {
  setStatus('正在生成验证码...', 'info')
  captchaData.value = null

  try {
    const response = await fetch('/api/captcha/generate')
    const result = await response.json()

    console.log('验证码生成响应:', response.status, response.statusText)
    console.log('验证码生成结果:', result)

    if (response.ok && result.success && result.data) {
      captchaData.value = result.data
      setStatus('验证码生成成功', 'success')
    } else {
      setStatus(`验证码生成失败: ${result.message || '未知错误'}`, 'error')
    }
  } catch (error) {
    setStatus(`验证码生成异常: ${error.message}`, 'error')
  }
}

const onImageLoad = () => {
  console.log('图片加载成功')
  setStatus('验证码图片加载成功', 'success')
}

const onImageError = (event) => {
  console.error('图片加载失败:', event)
  setStatus('验证码图片加载失败', 'error')
}
</script>

<style scoped>
.simple-captcha {
  max-width: 600px;
  margin: 20px auto;
  padding: 20px;
  border: 1px solid #ddd;
  border-radius: 8px;
}

.test-buttons {
  margin: 20px 0;
}

.test-buttons button {
  margin: 5px;
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  background-color: #007bff;
  color: white;
  cursor: pointer;
}

.test-buttons button:hover {
  background-color: #0056b3;
}

.status {
  margin: 20px 0;
}

.message {
  padding: 10px;
  border-radius: 4px;
  font-weight: bold;
}

.message.info {
  background-color: #d1ecf1;
  color: #0c5460;
  border: 1px solid #bee5eb;
}

.message.success {
  background-color: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.message.error {
  background-color: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

.captcha-display {
  margin: 20px 0;
}

.info {
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 4px;
}

.info p {
  margin: 5px 0;
}

.image-container {
  text-align: center;
}

.no-image {
  padding: 40px;
  background-color: #f8f9fa;
  border: 2px dashed #ddd;
  border-radius: 4px;
  color: #666;
}
</style>
