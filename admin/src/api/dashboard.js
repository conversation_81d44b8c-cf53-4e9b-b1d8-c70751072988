import { request } from '@/api/request'

/**
 * 仪表板 API
 */
export const dashboardApi = {
  /**
   * 获取仪表板统计数据
   */
  getStatistics() {
    return request('/api/recycle-orders/dashboard/statistics', {
      method: 'GET'
    })
  },

  /**
   * 获取趋势图数据
   * @param {string} period 时间周期 (week/month/year)
   */
  getTrendData(period = 'week') {
    return request(`/api/recycle-orders/dashboard/trends?period=${period}`, {
      method: 'GET'
    })
  }
}

export default dashboardApi
