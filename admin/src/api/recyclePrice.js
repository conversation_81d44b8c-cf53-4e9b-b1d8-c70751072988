import { request } from '@/api/request'

/**
 * 回收价格管理 API
 */
export const recyclePriceApi = {
  /**
   * 分页查询回收单价
   * @param {Object} queryDto 查询条件
   * @param {number} page 页码（从0开始）
   * @param {number} size 每页大小
   */
  searchPrices(queryDto = {}, page = 0, size = 10) {
    const url = `/api/recycle-prices/search?page=${page}&size=${size}`
    return request(url, {
      method: 'POST',
      body: JSON.stringify(queryDto)
    })
  },

  /**
   * 查询所有当前有效价格
   */
  getCurrentPrices() {
    return request('/api/recycle-prices/current', {
      method: 'GET'
    })
  },

  /**
   * 根据ID查询回收单价
   * @param {number} id 价格ID
   */
  getPriceById(id) {
    return request(`/api/recycle-prices/${id}`, {
      method: 'GET'
    })
  },

  /**
   * 创建回收单价
   * @param {Object} priceDto 价格信息
   */
  createPrice(priceDto) {
    return request('/api/recycle-prices', {
      method: 'POST',
      body: JSON.stringify(priceDto)
    })
  },

  /**
   * 更新回收单价
   * @param {number} id 价格ID
   * @param {Object} priceDto 价格信息
   */
  updatePrice(id, priceDto) {
    return request(`/api/recycle-prices/${id}`, {
      method: 'PUT',
      body: JSON.stringify(priceDto)
    })
  },

  /**
   * 删除回收单价
   * @param {number} id 价格ID
   */
  deletePrice(id) {
    return request(`/api/recycle-prices/${id}`, {
      method: 'DELETE'
    })
  },

  /**
   * 批量删除回收单价
   * @param {Array<number>} ids 价格ID数组
   */
  deletePrices(ids) {
    return request('/api/recycle-prices/batch', {
      method: 'DELETE',
      body: JSON.stringify(ids)
    })
  },

  /**
   * 启用回收单价
   * @param {number} id 价格ID
   */
  enablePrice(id) {
    return request(`/api/recycle-prices/${id}/enable`, {
      method: 'POST'
    })
  },

  /**
   * 禁用回收单价
   * @param {number} id 价格ID
   */
  disablePrice(id) {
    return request(`/api/recycle-prices/${id}/disable`, {
      method: 'POST'
    })
  },

  /**
   * 设置为当前有效价格
   * @param {number} id 价格ID
   */
  setAsCurrentPrice(id) {
    return request(`/api/recycle-prices/${id}/set-current`, {
      method: 'POST'
    })
  },

  /**
   * 获取所有物品类型
   */
  getAllItemTypes() {
    return request('/api/recycle-prices/item-types', {
      method: 'GET'
    })
  },

  /**
   * 根据物品类型获取物品名称
   * @param {string} itemType 物品类型
   */
  getItemNamesByType(itemType) {
    return request(`/api/recycle-prices/item-names/${encodeURIComponent(itemType)}`, {
      method: 'GET'
    })
  }
}

export default recyclePriceApi
