import { request } from '@/api/request'

/**
 * 回收订单管理 API
 */
export const orderApi = {
  /**
   * 创建回收订单
   * @param {Object} orderDto 订单信息
   */
  createOrder(orderDto) {
    return request('/api/recycle-orders', {
      method: 'POST',
      body: JSON.stringify(orderDto)
    })
  },

  /**
   * 根据ID获取订单详情
   * @param {number} id 订单ID
   */
  getOrderById(id) {
    return request(`/api/recycle-orders/${id}`, {
      method: 'GET'
    })
  },

  /**
   * 根据订单号获取订单
   * @param {string} orderNo 订单号
   */
  getOrderByOrderNo(orderNo) {
    return request(`/api/recycle-orders/order-no/${encodeURIComponent(orderNo)}`, {
      method: 'GET'
    })
  },

  /**
   * 更新订单信息
   * @param {number} id 订单ID
   * @param {Object} orderDto 订单信息
   */
  updateOrder(id, orderDto) {
    return request(`/api/recycle-orders/${id}`, {
      method: 'PUT',
      body: JSON.stringify(orderDto)
    })
  },

  /**
   * 更新订单状态
   * @param {number} id 订单ID
   * @param {number} orderStatus 订单状态
   */
  updateOrderStatus(id, orderStatus) {
    return request(`/api/recycle-orders/${id}/status?orderStatus=${orderStatus}`, {
      method: 'PUT'
    })
  },

  /**
   * 删除订单
   * @param {number} id 订单ID
   */
  deleteOrder(id) {
    return request(`/api/recycle-orders/${id}`, {
      method: 'DELETE'
    })
  },

  /**
   * 查询订单列表
   * @param {Object} queryDto 查询条件
   */
  queryOrders(queryDto = {}) {
    // 构建查询参数
    const params = new URLSearchParams()

    Object.keys(queryDto).forEach(key => {
      if (queryDto[key] !== null && queryDto[key] !== undefined && queryDto[key] !== '') {
        params.append(key, queryDto[key])
      }
    })

    const url = `/api/recycle-orders?${params.toString()}`
    return request(url, {
      method: 'GET'
    })
  },

  /**
   * 生成订单号
   */
  generateOrderNo() {
    return request('/api/recycle-orders/generate-order-no', {
      method: 'GET'
    })
  },

  /**
   * 导出订单数据
   * @param {Object} queryDto 查询条件
   */
  exportOrders(queryDto = {}) {
    const params = new URLSearchParams()

    Object.keys(queryDto).forEach(key => {
      if (queryDto[key] !== null && queryDto[key] !== undefined && queryDto[key] !== '') {
        params.append(key, queryDto[key])
      }
    })

    const url = `/api/recycle-orders/export?${params.toString()}`
    return request(url, {
      method: 'GET',
      responseType: 'blob'
    })
  },

  /**
   * 获取订单统计信息
   */
  getOrderStatistics() {
    return request('/api/recycle-orders/statistics', {
      method: 'GET'
    })
  },

  /**
   * 批量更新订单状态
   * @param {Array<number>} ids 订单ID数组
   * @param {number} orderStatus 订单状态
   */
  batchUpdateOrderStatus(ids, orderStatus) {
    return request('/api/recycle-orders/batch/status', {
      method: 'PUT',
      body: JSON.stringify({
        ids,
        orderStatus
      })
    })
  },

  /**
   * 批量删除订单
   * @param {Array<number>} ids 订单ID数组
   */
  batchDeleteOrders(ids) {
    return request('/api/recycle-orders/batch', {
      method: 'DELETE',
      body: JSON.stringify(ids)
    })
  },

  /**
   * 分配确认人员
   * @param {Object} assignDto 分配确认人信息
   */
  assignConfirmer(assignDto) {
    return request('/api/recycle-orders/assign-confirmer', {
      method: 'POST',
      body: JSON.stringify(assignDto)
    })
  },

  /**
   * 确认订单
   * @param {Object} confirmDto 确认订单信息
   */
  confirmOrder(confirmDto) {
    return request('/api/recycle-orders/confirm', {
      method: 'POST',
      body: JSON.stringify(confirmDto)
    })
  },

  /**
   * 查询待确认订单
   * @param {number} confirmerId 确认人ID
   */
  getPendingConfirmOrders(confirmerId) {
    return request(`/api/recycle-orders/pending-confirm/${confirmerId}`, {
      method: 'GET'
    })
  }
}

export default orderApi
