// 通用请求方法，自动加token
import {setToken} from "@/utils/auth.js";

export async function request(url, options = {}) {
  const token = localStorage.getItem('token')
  const config = {
    headers: {
      'Content-Type': 'application/json',
      ...(token ? { 'Authorization': `Bearer ${token}` } : {}),
      ...options.headers
    },
    ...options
  }
  try {
    const response = await fetch(url, config)
    console.log("response", response)
    if (response.status === 401 || response.status === 403) {
      setToken("");
      window.location.reload()
      throw new Error(result.message || `HTTP error! status: ${response.status}`)
    }
    const result = await response.json()
    console.log("result", result)
    if (!response.ok) {
      throw new Error(result.message || `HTTP error! status: ${response.status}`)
    }
    return result
  } catch (error) {
    console.error('API request failed:', error)
    throw error
  }
}
