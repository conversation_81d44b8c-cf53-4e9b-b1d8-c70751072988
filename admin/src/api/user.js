import { request } from './request.js'

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || ''

export const userApi = {
  getUsers(params = {}) {
    const queryString = new URLSearchParams(params).toString()
    return request(`${API_BASE_URL}/api/users${queryString ? '?' + queryString : ''}`)
  },
  getAllUsers() {
    return request(`${API_BASE_URL}/api/users?page=0&size=100`)
  },
  getAllUsersByType(userType) {
    return request(`${API_BASE_URL}/api/users/type/${userType}`)
  },
  createUser(user) {
    return request(`${API_BASE_URL}/api/users`, {
      method: 'POST',
      body: JSON.stringify(user)
    })
  },
  updateUser(id, user) {
    return request(`${API_BASE_URL}/api/users/${id}`, {
      method: 'PUT',
      body: JSON.stringify(user)
    })
  },
  deleteUser(id) {
    return request(`${API_BASE_URL}/api/users/${id}`, {
      method: 'DELETE'
    })
  },
  lockUser(id) {
    return request(`${API_BASE_URL}/api/users/${id}/lock`, {
      method: 'POST'
    })
  },
  unlockUser(id) {
    return request(`${API_BASE_URL}/api/users/${id}/unlock`, {
      method: 'POST'
    })
  },
  resetPassword(id, newPassword) {
    return request(`${API_BASE_URL}/api/users/${id}/reset-password?newPassword=${encodeURIComponent(newPassword)}`, {
      method: 'POST'
    })
  }
}
