import { request } from '@/api/request'

/**
 * 数据字典管理 API
 */
export const dictionaryApi = {
  /**
   * 分页查询数据字典
   * @param {Object} queryDto 查询条件
   * @param {number} page 页码（从0开始）
   * @param {number} size 每页大小
   */
  searchDictionaries(queryDto = {}, page = 0, size = 10) {
    const url = `/api/dictionaries/search?page=${page}&size=${size}`
    return request(url, {
      method: 'POST',
      body: JSON.stringify(queryDto)
    })
  },

  /**
   * 查询所有有效数据字典
   */
  getActiveDictionaries() {
    return request('/api/dictionaries/active', {
      method: 'GET'
    })
  },

  /**
   * 根据ID查询数据字典
   * @param {number} id 字典ID
   */
  getDictionaryById(id) {
    return request(`/api/dictionaries/${id}`, {
      method: 'GET'
    })
  },

  /**
   * 根据分类查询数据字典
   * @param {string} category 字典分类
   */
  getDictionariesByCategory(category) {
    return request(`/api/dictionaries/category/${encodeURIComponent(category)}`, {
      method: 'GET'
    })
  },

  /**
   * 根据分类和名称查询数据字典
   * @param {string} category 字典分类
   * @param {string} name 字典名称
   */
  getDictionaryByCategoryAndName(category, name) {
    return request(`/api/dictionaries/category/${encodeURIComponent(category)}/name/${encodeURIComponent(name)}`, {
      method: 'GET'
    })
  },

  /**
   * 创建数据字典
   * @param {Object} dictionaryDto 字典信息
   */
  createDictionary(dictionaryDto) {
    return request('/api/dictionaries', {
      method: 'POST',
      body: JSON.stringify(dictionaryDto)
    })
  },

  /**
   * 更新数据字典
   * @param {number} id 字典ID
   * @param {Object} dictionaryDto 字典信息
   */
  updateDictionary(id, dictionaryDto) {
    return request(`/api/dictionaries/${id}`, {
      method: 'PUT',
      body: JSON.stringify(dictionaryDto)
    })
  },

  /**
   * 删除数据字典
   * @param {number} id 字典ID
   */
  deleteDictionary(id) {
    return request(`/api/dictionaries/${id}`, {
      method: 'DELETE'
    })
  },

  /**
   * 批量删除数据字典
   * @param {Array<number>} ids 字典ID数组
   */
  deleteDictionaries(ids) {
    return request('/api/dictionaries/batch', {
      method: 'DELETE',
      body: JSON.stringify(ids)
    })
  },

  /**
   * 启用数据字典
   * @param {number} id 字典ID
   */
  enableDictionary(id) {
    return request(`/api/dictionaries/${id}/enable`, {
      method: 'POST'
    })
  },

  /**
   * 禁用数据字典
   * @param {number} id 字典ID
   */
  disableDictionary(id) {
    return request(`/api/dictionaries/${id}/disable`, {
      method: 'POST'
    })
  },

  /**
   * 批量启用数据字典
   * @param {Array<number>} ids 字典ID数组
   */
  enableDictionaries(ids) {
    return request('/api/dictionaries/batch/enable', {
      method: 'POST',
      body: JSON.stringify(ids)
    })
  },

  /**
   * 批量禁用数据字典
   * @param {Array<number>} ids 字典ID数组
   */
  disableDictionaries(ids) {
    return request('/api/dictionaries/batch/disable', {
      method: 'POST',
      body: JSON.stringify(ids)
    })
  },

  /**
   * 获取所有字典分类
   */
  getAllCategories() {
    return request('/api/dictionaries/categories', {
      method: 'GET'
    })
  },

  /**
   * 根据分类获取字典映射
   * @param {string} category 字典分类
   */
  getDictionaryMapByCategory(category) {
    return request(`/api/dictionaries/category/${encodeURIComponent(category)}/map`, {
      method: 'GET'
    })
  },

  /**
   * 根据分类获取字典列表
   * @param {string} category 字典分类
   */
  getDictionaryListByCategory(category) {
    return request(`/api/dictionaries/category/${encodeURIComponent(category)}/list`, {
      method: 'GET'
    })
  }
}

export default dictionaryApi
