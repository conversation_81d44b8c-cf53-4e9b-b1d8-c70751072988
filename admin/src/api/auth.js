import { request } from './request.js'

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || ''

export const authApi = {
  login(loginData) {
    return request(`${API_BASE_URL}/api/auth/login`, {
      method: 'POST',
      body: JSON.stringify(loginData)
    })
  },
  // 发送验证码
  sendCode(telephone) {
    return request(`${API_BASE_URL}/api/auth/send-code/${telephone}`, {
      method: 'POST'
    })
  },
  // 验证码登录
  loginWithCode(loginData) {
    return request(`${API_BASE_URL}/api/auth/login/code`, {
      method: 'POST',
      body: JSON.stringify(loginData)
    })
  },
  getUserInfo() {
    return request(`${API_BASE_URL}/api/user/info`)
  }
}
