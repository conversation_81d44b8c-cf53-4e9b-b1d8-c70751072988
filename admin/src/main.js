import { createApp } from 'vue'
import App from './App.vue'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import { enableDebugMode, printEnvironmentInfo } from './utils/debug.js'
import router from './router'
const app = createApp(App)

import zhCn from 'element-plus/es/locale/lang/zh-cn'
// import locale from 'element-plus/lib/locale'

// 设置语言
// locale.use(lang)
// 注册所有图标
for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}

app.use(ElementPlus,{
  locale: zhCn,
})

// 在开发环境中启用调试模式
if (import.meta.env.DEV) {
  enableDebugMode()
  printEnvironmentInfo()
}
app.use(router)
app.mount('#app')
