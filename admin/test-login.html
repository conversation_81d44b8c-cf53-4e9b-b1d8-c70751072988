<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-title {
            color: #333;
            border-bottom: 2px solid #667eea;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .api-info {
            background: #e3f2fd;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .api-endpoint {
            font-family: monospace;
            background: #f5f5f5;
            padding: 5px;
            border-radius: 3px;
            color: #d32f2f;
        }
        .feature-list {
            list-style-type: none;
            padding: 0;
        }
        .feature-list li {
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }
        .feature-list li:before {
            content: "✓ ";
            color: #4caf50;
            font-weight: bold;
        }
        .code-example {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
            font-family: monospace;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <h1>叮咚回收管理系统 - 登录功能测试</h1>
    
    <div class="test-section">
        <h2 class="test-title">📱 手机号验证码登录功能</h2>
        
        <div class="api-info">
            <h3>API 接口信息</h3>
            <p><strong>发送验证码：</strong> <span class="api-endpoint">POST /api/auth/send-code/{telephone}</span></p>
            <p><strong>验证码登录：</strong> <span class="api-endpoint">POST /api/auth/login/code</span></p>
        </div>

        <h3>✅ 已实现的功能</h3>
        <ul class="feature-list">
            <li>手机号输入框（支持11位手机号格式验证）</li>
            <li>验证码输入框（支持6位数字验证码）</li>
            <li>发送验证码按钮（带倒计时功能，60秒后可重发）</li>
            <li>表单验证（手机号格式、验证码必填）</li>
            <li>登录按钮（调用验证码登录接口）</li>
            <li>响应式设计（支持移动端）</li>
            <li>错误提示和成功提示</li>
            <li>登录成功后跳转到主页</li>
        </ul>

        <h3>🎨 UI 特性</h3>
        <ul class="feature-list">
            <li>现代化渐变背景设计</li>
            <li>毛玻璃效果的登录卡片</li>
            <li>优雅的输入框样式和焦点效果</li>
            <li>绿色主题的发送验证码按钮</li>
            <li>紫色主题的登录按钮</li>
            <li>流畅的动画效果</li>
        </ul>

        <h3>📱 移动端适配</h3>
        <ul class="feature-list">
            <li>响应式布局，适配不同屏幕尺寸</li>
            <li>移动端优化的按钮大小和间距</li>
            <li>触摸友好的交互设计</li>
        </ul>
    </div>

    <div class="test-section">
        <h2 class="test-title">🔧 技术实现</h2>
        
        <h3>前端技术栈</h3>
        <ul class="feature-list">
            <li>Vue 3 + Composition API</li>
            <li>Element Plus UI 组件库</li>
            <li>Vue Router 路由管理</li>
            <li>Vite 构建工具</li>
        </ul>

        <h3>核心代码示例</h3>
        <div class="code-example">// 发送验证码
const sendCode = async () => {
  const result = await authApi.sendCode(telephone)
  if (result.success) {
    ElMessage.success('验证码发送成功')
    startCountdown()
  }
}

// 验证码登录
const handleLogin = async () => {
  const result = await authApi.loginWithCode({
    telephone: loginForm.value.telephone,
    code: loginForm.value.code
  })
  if (result.success) {
    setToken(result.data.token)
    router.push("/")
  }</div>
    </div>

    <div class="test-section">
        <h2 class="test-title">🚀 测试步骤</h2>
        <ol>
            <li>打开登录页面</li>
            <li>输入有效的手机号（11位数字，以1开头）</li>
            <li>点击"发送验证码"按钮</li>
            <li>等待验证码发送成功提示</li>
            <li>输入收到的6位验证码</li>
            <li>点击"立即登录"按钮</li>
            <li>验证登录成功并跳转到主页</li>
        </ol>
    </div>

    <div class="test-section">
        <h2 class="test-title">📝 注意事项</h2>
        <ul class="feature-list">
            <li>手机号必须符合中国大陆手机号格式（1开头，11位数字）</li>
            <li>验证码为6位数字</li>
            <li>发送验证码后有60秒倒计时，期间无法重复发送</li>
            <li>登录成功后会自动清理表单数据</li>
            <li>所有错误都会有相应的提示信息</li>
        </ul>
    </div>

    <script>
        // 简单的页面交互
        document.addEventListener('DOMContentLoaded', function() {
            console.log('登录功能测试页面已加载');
            console.log('请访问 http://localhost:5173 查看实际的登录页面');
        });
    </script>
</body>
</html>
