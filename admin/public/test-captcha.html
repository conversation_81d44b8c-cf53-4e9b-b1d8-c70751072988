<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>验证码测试页面</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }

        .test-container {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }

        .test-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
        }

        .status {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
            font-weight: bold;
        }

        .status.success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .status.error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .status.info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }

        button {
            padding: 8px 16px;
            margin: 5px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            background-color: #007bff;
            color: white;
        }

        button:hover {
            background-color: #0056b3;
        }

        .captcha-image {
            position: relative;
            display: inline-block;
            border: 2px solid #ddd;
            border-radius: 4px;
            cursor: crosshair;
            margin: 10px 0;
        }

        .captcha-image img {
            display: block;
            height: auto;
        }

        .click-point {
            position: absolute;
            width: 20px;
            height: 20px;
            background-color: #ff4444;
            border: 2px solid white;
            border-radius: 50%;
            transform: translate(-50%, -50%);
            color: white;
            font-size: 12px;
            font-weight: bold;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        .info-panel {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <h1>验证码功能测试</h1>

    <div class="test-container">
        <div class="test-title">1. API连接测试</div>
        <button onclick="testAPI()">测试API连接</button>
        <div id="api-status"></div>
    </div>

    <div class="test-container">
        <div class="test-title">2. 验证码生成测试</div>
        <button onclick="generateCaptcha()">生成验证码</button>
        <button onclick="clearClicks()">清除点击</button>
        <div id="captcha-status"></div>
        <div id="captcha-info" class="info-panel" style="display: none;"></div>
        <div id="captcha-container"></div>
    </div>

    <div class="test-container">
        <div class="test-title">3. 验证码验证测试</div>
        <button onclick="verifyCaptcha()">验证验证码</button>
        <div id="verify-status"></div>
    </div>

    <script>
        let currentCaptcha = null;
        let clickPoints = [];

        function setStatus(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="status ${type}">${message}</div>`;
        }

        async function testAPI() {
            setStatus('api-status', '正在测试API连接...', 'info');

            try {
                const response = await fetch('/api/captcha/generate');
                const result = await response.json();

                if (response.ok && result.success) {
                    setStatus('api-status', 'API连接成功', 'success');
                    console.log('API测试结果:', result);
                } else {
                    setStatus('api-status', `API连接失败: ${result.message || '未知错误'}`, 'error');
                }
            } catch (error) {
                setStatus('api-status', `API连接异常: ${error.message}`, 'error');
            }
        }

        async function generateCaptcha() {
            setStatus('captcha-status', '正在生成验证码...', 'info');

            try {
                const response = await fetch('/api/captcha/generate');
                const result = await response.json();

                console.log('验证码生成响应:', response.status, response.statusText);
                console.log('验证码生成结果:', result);

                if (response.ok && result.success && result.data) {
                    currentCaptcha = result.data;
                    clickPoints = [];

                    setStatus('captcha-status', '验证码生成成功', 'success');

                    // 显示验证码信息
                    const infoPanel = document.getElementById('captcha-info');
                    infoPanel.style.display = 'block';
                    infoPanel.innerHTML = `
验证码ID: ${currentCaptcha.captchaId}
提示文字: ${currentCaptcha.promptText}
图片尺寸: ${currentCaptcha.width}x${currentCaptcha.height}
有效期: ${currentCaptcha.expireTime}秒
图片数据长度: ${currentCaptcha.imageBase64?.length || 0}
                    `;

                    // 显示验证码图片
                    displayCaptcha(currentCaptcha);
                } else {
                    setStatus('captcha-status', `验证码生成失败: ${result.message || '未知错误'}`, 'error');
                }
            } catch (error) {
                setStatus('captcha-status', `验证码生成异常: ${error.message}`, 'error');
            }
        }

        function displayCaptcha(captcha) {
            const container = document.getElementById('captcha-container');

            container.innerHTML = `
                <div class="captcha-image" onclick="handleImageClick(event)">
                    <img src="${captcha.imageBase64}" alt="验证码" onload="onImageLoad()" onerror="onImageError()">
                </div>
            `;
        }

        function handleImageClick(event) {
            if (!currentCaptcha) return;

            const rect = event.target.getBoundingClientRect();
            const x = Math.round(event.clientX - rect.left);
            const y = Math.round(event.clientY - rect.top);

            clickPoints.push({x, y});

            // 显示点击点
            const point = document.createElement('div');
            point.className = 'click-point';
            point.style.left = x + 'px';
            point.style.top = y + 'px';
            point.textContent = clickPoints.length;

            event.target.parentElement.appendChild(point);

            console.log(`点击位置: (${x}, ${y})`);

            // 如果点击了2个点，自动验证
            if (clickPoints.length >= 2) {
                setTimeout(verifyCaptcha, 500);
            }
        }

        function clearClicks() {
            clickPoints = [];
            const points = document.querySelectorAll('.click-point');
            points.forEach(point => point.remove());
            setStatus('captcha-status', '已清除点击记录', 'info');
        }

        async function verifyCaptcha() {
            if (!currentCaptcha || clickPoints.length === 0) {
                setStatus('verify-status', '请先生成验证码并点击', 'error');
                return;
            }

            setStatus('verify-status', '正在验证验证码...', 'info');

            try {
                const verifyData = {
                    captchaId: currentCaptcha.captchaId,
                    clickPoints: clickPoints
                };

                const response = await fetch('/api/captcha/verify', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(verifyData)
                });

                const result = await response.json();

                if (response.ok && result.success) {
                    setStatus('verify-status', '验证码验证成功！', 'success');
                } else {
                    setStatus('verify-status', `验证码验证失败: ${result.message || '未知错误'}`, 'error');
                    // 验证失败后重新生成
                    setTimeout(generateCaptcha, 1000);
                }
            } catch (error) {
                setStatus('verify-status', `验证码验证异常: ${error.message}`, 'error');
            }
        }

        function onImageLoad() {
            console.log('验证码图片加载成功');
        }

        function onImageError() {
            console.error('验证码图片加载失败');
            setStatus('captcha-status', '验证码图片加载失败', 'error');
        }

        // 页面加载时自动测试API
        window.onload = function() {
            console.log('验证码测试页面已加载');
            testAPI();
        };
    </script>
</body>
</html>
