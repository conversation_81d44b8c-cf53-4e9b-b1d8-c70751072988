<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>验证码API测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            padding: 8px 16px;
            margin: 5px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            background-color: #007bff;
            color: white;
        }
        button:hover {
            background-color: #0056b3;
        }
        img {
            max-width: 100%;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <h1>验证码API测试页面</h1>

    <div class="test-section">
        <h2>1. 测试后端直连</h2>
        <button onclick="testBackendDirect()">测试后端直连 (8081)</button>
        <div id="backend-result" class="result"></div>
    </div>

    <div class="test-section">
        <h2>2. 测试前端代理</h2>
        <button onclick="testFrontendProxy()">测试前端代理 (5173)</button>
        <div id="proxy-result" class="result"></div>
    </div>

    <div class="test-section">
        <h2>3. 验证码显示测试</h2>
        <button onclick="generateAndShowCaptcha()">生成并显示验证码</button>
        <div id="captcha-result" class="result"></div>
        <div id="captcha-image"></div>
    </div>

    <div class="test-section">
        <h2>4. 网络请求详情</h2>
        <button onclick="showNetworkDetails()">显示网络请求详情</button>
        <div id="network-result" class="result"></div>
    </div>

    <script>
        // 测试后端直连
        async function testBackendDirect() {
            const resultDiv = document.getElementById('backend-result');
            resultDiv.textContent = '正在测试后端直连...';
            resultDiv.className = 'result info';

            try {
                const response = await fetch('http://localhost:8081/api/captcha/generate');
                const result = await response.json();

                if (response.ok && result.success) {
                    resultDiv.textContent = `后端直连成功\n状态码: ${response.status}\n响应: ${JSON.stringify(result, null, 2)}`;
                    resultDiv.className = 'result success';
                } else {
                    resultDiv.textContent = `后端直连失败\n状态码: ${response.status}\n响应: ${JSON.stringify(result, null, 2)}`;
                    resultDiv.className = 'result error';
                }
            } catch (error) {
                resultDiv.textContent = `后端直连异常: ${error.message}`;
                resultDiv.className = 'result error';
            }
        }

        // 测试前端代理
        async function testFrontendProxy() {
            const resultDiv = document.getElementById('proxy-result');
            resultDiv.textContent = '正在测试前端代理...';
            resultDiv.className = 'result info';

            try {
                const response = await fetch('/api/captcha/generate');
                const result = await response.json();

                if (response.ok && result.success) {
                    resultDiv.textContent = `前端代理成功\n状态码: ${response.status}\n响应: ${JSON.stringify(result, null, 2)}`;
                    resultDiv.className = 'result success';
                } else {
                    resultDiv.textContent = `前端代理失败\n状态码: ${response.status}\n响应: ${JSON.stringify(result, null, 2)}`;
                    resultDiv.className = 'result error';
                }
            } catch (error) {
                resultDiv.textContent = `前端代理异常: ${error.message}`;
                resultDiv.className = 'result error';
            }
        }

        // 生成并显示验证码
        async function generateAndShowCaptcha() {
            const resultDiv = document.getElementById('captcha-result');
            const imageDiv = document.getElementById('captcha-image');

            resultDiv.textContent = '正在生成验证码...';
            resultDiv.className = 'result info';
            imageDiv.innerHTML = '';

            try {
                const response = await fetch('/api/captcha/generate');
                const result = await response.json();

                if (response.ok && result.success && result.data) {
                    const data = result.data;
                    resultDiv.textContent = `验证码生成成功\n验证码ID: ${data.captchaId}\n提示文字: ${data.promptText}\n图片尺寸: ${data.width}x${data.height}\n有效期: ${data.expireTime}秒\n图片数据长度: ${data.imageBase64?.length || 0}`;
                    resultDiv.className = 'result success';

                    // 显示图片
                    if (data.imageBase64) {
                        const img = document.createElement('img');
                        img.src = data.imageBase64;
                        img.alt = '验证码';
                        img.onload = () => {
                            console.log('图片加载成功');
                        };
                        img.onerror = (e) => {
                            console.error('图片加载失败:', e);
                            resultDiv.textContent += '\n图片加载失败';
                            resultDiv.className = 'result error';
                        };
                        imageDiv.appendChild(img);
                    }
                } else {
                    resultDiv.textContent = `验证码生成失败\n状态码: ${response.status}\n响应: ${JSON.stringify(result, null, 2)}`;
                    resultDiv.className = 'result error';
                }
            } catch (error) {
                resultDiv.textContent = `验证码生成异常: ${error.message}`;
                resultDiv.className = 'result error';
            }
        }

        // 显示网络请求详情
        async function showNetworkDetails() {
            const resultDiv = document.getElementById('network-result');

            const info = {
                '当前页面URL': window.location.href,
                '用户代理': navigator.userAgent,
                '时间戳': new Date().toISOString(),
                '测试环境': {
                    '前端地址': 'http://localhost:5173',
                    '后端地址': 'http://localhost:8081',
                    '代理路径': '/api -> http://localhost:8081/api'
                }
            };

            resultDiv.textContent = JSON.stringify(info, null, 2);
            resultDiv.className = 'result info';
        }

        // 页面加载时自动运行测试
        window.onload = function() {
            console.log('验证码API测试页面已加载');
            console.log('可用测试函数:');
            console.log('- testBackendDirect() - 测试后端直连');
            console.log('- testFrontendProxy() - 测试前端代理');
            console.log('- generateAndShowCaptcha() - 生成并显示验证码');
        };
    </script>
</body>
</html>
