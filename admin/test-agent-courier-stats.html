<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>代理人和跑腿员统计功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-title {
            color: #333;
            border-bottom: 2px solid #667eea;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 24px;
            margin: 20px 0;
        }
        .stat-card {
            background: white;
            padding: 24px;
            border-radius: 16px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
            border: 1px solid #e5e7eb;
            display: flex;
            align-items: center;
            gap: 16px;
            transition: all 0.3s ease;
        }
        .stat-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 12px 24px rgba(0, 0, 0, 0.1);
        }
        .stat-icon {
            width: 60px;
            height: 60px;
            border-radius: 16px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
        }
        .stat-content {
            flex: 1;
        }
        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            color: #1f2937;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.9rem;
            color: #6b7280;
        }
        .stat-change {
            font-size: 0.9rem;
            font-weight: 600;
            padding: 4px 8px;
            border-radius: 8px;
        }
        .stat-change.positive {
            color: #10b981;
            background: rgba(16, 185, 129, 0.1);
        }
        .stat-change.negative {
            color: #ef4444;
            background: rgba(239, 68, 68, 0.1);
        }
        .feature-list {
            list-style-type: none;
            padding: 0;
        }
        .feature-list li {
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }
        .feature-list li:before {
            content: "✓ ";
            color: #4caf50;
            font-weight: bold;
        }
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-weight: bold;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
        }
        .comparison-table th,
        .comparison-table td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        .comparison-table th {
            background: #f5f5f5;
            font-weight: bold;
        }
        .highlight {
            background: #fff3cd;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <h1>代理人和跑腿员统计功能测试</h1>
    
    <div class="test-section">
        <h2 class="test-title">📊 新增统计功能</h2>
        
        <div class="status success">
            ✅ 已成功添加代理人和跑腿员统计功能
        </div>
        
        <h3>新增统计指标</h3>
        <ul class="feature-list">
            <li><strong>代理人总数</strong>：显示系统中注册的代理人数量</li>
            <li><strong>跑腿员总数</strong>：显示系统中活跃的跑腿员数量</li>
            <li><strong>增长趋势</strong>：显示相对于上期的增长率</li>
            <li><strong>图标设计</strong>：使用合适的图标区分不同角色</li>
        </ul>
    </div>

    <div class="test-section">
        <h2 class="test-title">🎯 统计卡片展示</h2>
        
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-icon">
                    <span style="font-size: 24px;">📊</span>
                </div>
                <div class="stat-content">
                    <div class="stat-number">1,234</div>
                    <div class="stat-label">今日回收量</div>
                </div>
                <div class="stat-change positive">+12.5%</div>
            </div>

            <div class="stat-card">
                <div class="stat-icon">
                    <span style="font-size: 24px;">💰</span>
                </div>
                <div class="stat-content">
                    <div class="stat-number">¥8,567</div>
                    <div class="stat-label">今日收入</div>
                </div>
                <div class="stat-change positive">+8.3%</div>
            </div>

            <div class="stat-card">
                <div class="stat-icon">
                    <span style="font-size: 24px;">👥</span>
                </div>
                <div class="stat-content">
                    <div class="stat-number">89</div>
                    <div class="stat-label">活跃用户</div>
                </div>
                <div class="stat-change positive">+5.2%</div>
            </div>

            <div class="stat-card">
                <div class="stat-icon">
                    <span style="font-size: 24px;">📦</span>
                </div>
                <div class="stat-content">
                    <div class="stat-number">456</div>
                    <div class="stat-label">待处理订单</div>
                </div>
                <div class="stat-change negative">-2.1%</div>
            </div>

            <div class="stat-card highlight">
                <div class="stat-icon">
                    <span style="font-size: 24px;">👨‍💼</span>
                </div>
                <div class="stat-content">
                    <div class="stat-number">156</div>
                    <div class="stat-label">代理人总数</div>
                </div>
                <div class="stat-change positive">+3.8%</div>
            </div>

            <div class="stat-card highlight">
                <div class="stat-icon">
                    <span style="font-size: 24px;">🚚</span>
                </div>
                <div class="stat-content">
                    <div class="stat-number">89</div>
                    <div class="stat-label">跑腿员总数</div>
                </div>
                <div class="stat-change positive">+6.7%</div>
            </div>
        </div>
    </div>

    <div class="test-section">
        <h2 class="test-title">📋 功能对比</h2>
        
        <table class="comparison-table">
            <thead>
                <tr>
                    <th>功能模块</th>
                    <th>原有功能</th>
                    <th>新增功能</th>
                    <th>状态</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>数据统计</td>
                    <td>4个统计卡片</td>
                    <td>6个统计卡片</td>
                    <td>✅ 已完成</td>
                </tr>
                <tr>
                    <td>统计指标</td>
                    <td>回收量、收入、用户、订单</td>
                    <td>+代理人、跑腿员</td>
                    <td>✅ 已完成</td>
                </tr>
                <tr>
                    <td>图标设计</td>
                    <td>基础业务图标</td>
                    <td>+角色专用图标</td>
                    <td>✅ 已完成</td>
                </tr>
                <tr>
                    <td>布局适配</td>
                    <td>4卡片网格</td>
                    <td>6卡片网格</td>
                    <td>✅ 已完成</td>
                </tr>
            </tbody>
        </table>
    </div>

    <div class="test-section">
        <h2 class="test-title">🔧 技术实现</h2>
        
        <h3>新增图标导入</h3>
        <pre><code>import { 
  TrendCharts, 
  Money, 
  User, 
  Box, 
  Document, 
  UserFilled, 
  PieChart, 
  ArrowRight,
  Van  // 新增跑腿员图标
} from '@element-plus/icons-vue'</code></pre>

        <h3>统计卡片HTML结构</h3>
        <pre><code>&lt;div class="stat-card"&gt;
  &lt;div class="stat-icon"&gt;
    &lt;el-icon&gt;&lt;UserFilled /&gt;&lt;/el-icon&gt;
  &lt;/div&gt;
  &lt;div class="stat-content"&gt;
    &lt;div class="stat-number"&gt;156&lt;/div&gt;
    &lt;div class="stat-label"&gt;代理人总数&lt;/div&gt;
  &lt;/div&gt;
  &lt;div class="stat-change positive"&gt;+3.8%&lt;/div&gt;
&lt;/div&gt;</code></pre>

        <h3>CSS布局优化</h3>
        <pre><code>/* 确保6个卡片时的布局 */
.stats-grid:has(.stat-card:nth-child(6)) {
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
}</code></pre>
    </div>

    <div class="test-section">
        <h2 class="test-title">📱 响应式设计</h2>
        
        <h3>不同屏幕尺寸下的显示效果</h3>
        <ul class="feature-list">
            <li><strong>桌面端 (>1200px)</strong>：3列布局，每行2个卡片</li>
            <li><strong>平板端 (768px-1200px)</strong>：2列布局，每行3个卡片</li>
            <li><strong>手机端 (<768px)</strong>：1列布局，垂直排列</li>
            <li><strong>小屏手机 (<480px)</strong>：单列布局，优化间距</li>
        </ul>

        <h3>布局自适应特性</h3>
        <ul class="feature-list">
            <li>Grid自动调整列数</li>
            <li>卡片最小宽度自适应</li>
            <li>间距自动调整</li>
            <li>内容溢出保护</li>
        </ul>
    </div>

    <div class="test-section">
        <h2 class="test-title">✅ 验证步骤</h2>
        
        <div class="status info">
            ℹ️ 请访问主页面验证新增的统计功能
        </div>
        
        <ol>
            <li>登录系统后进入主页面</li>
            <li>观察数据统计区域是否显示6个统计卡片</li>
            <li>确认新增的"代理人总数"和"跑腿员总数"卡片</li>
            <li>验证图标是否正确显示（👨‍💼和🚚）</li>
            <li>测试不同屏幕尺寸下的布局效果</li>
            <li>确认悬停效果和动画是否正常</li>
        </ol>
        
        <h3>预期效果</h3>
        <ul class="feature-list">
            <li>✅ 显示6个统计卡片，布局合理</li>
            <li>✅ 代理人统计：156 (+3.8%)</li>
            <li>✅ 跑腿员统计：89 (+6.7%)</li>
            <li>✅ 图标设计美观，符合角色特征</li>
            <li>✅ 响应式布局，适配各种设备</li>
            <li>✅ 悬停效果流畅，用户体验良好</li>
        </ul>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            console.log('代理人和跑腿员统计功能测试页面已加载');
            console.log('请访问主页面验证实际的统计功能');
            
            // 模拟统计卡片点击
            const statCards = document.querySelectorAll('.stat-card');
            statCards.forEach((card, index) => {
                card.addEventListener('click', function() {
                    const label = this.querySelector('.stat-label').textContent;
                    const number = this.querySelector('.stat-number').textContent;
                    console.log(`点击了${label}：${number}`);
                    
                    if (index >= 4) { // 新增的统计卡片
                        alert(`新增统计功能：${label}\n数值：${number}\n在实际页面中会显示详细数据`);
                    }
                });
            });
        });
    </script>
</body>
</html>
