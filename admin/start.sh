#!/bin/bash

echo "==================================="
echo "叮咚回收管理系统 - 前端启动脚本"
echo "==================================="

# 检查Node.js是否安装
if ! command -v node &> /dev/null; then
    echo "Node.js 未安装，请先安装 Node.js"
    exit 1
fi

# 检查npm是否安装
if ! command -v npm &> /dev/null; then
    echo "npm 未安装，请先安装 npm"
    exit 1
fi

echo "Node.js 版本: $(node --version)"
echo "npm 版本: $(npm --version)"
echo ""

# 检查是否存在node_modules
if [ ! -d "node_modules" ]; then
    echo "📦 正在安装依赖..."
    npm install

    if [ $? -ne 0 ]; then
        echo "依赖安装失败"
        exit 1
    fi
    echo "依赖安装完成"
else
    echo "依赖已存在"
fi

echo ""
echo "🔍 检查后端服务..."
if curl -s http://localhost:8081/api/captcha/generate > /dev/null 2>&1; then
    echo "后端服务正常运行"
else
    echo "⚠️  后端服务未启动或无法访问"
    echo "请确保后端服务运行在 http://localhost:8081"
    echo "可以通过以下方式启动后端:"
    echo "cd ../dingdong-server && mvn spring-boot:run"
    echo ""
fi

echo ""
echo "🚀 启动开发服务器..."
echo "📱 前端地址: http://localhost:5173"
echo "🔗 后端地址: http://localhost:8081"
echo "📚 API文档: http://localhost:8081/swagger-ui.html"
echo ""
echo "默认登录信息:"
echo "用户名: admin"
echo "密码: 123456"
echo "验证码: 按页面提示点击汉字"
echo ""
echo "调试命令 (在浏览器控制台中使用):"
echo "debugAPI.checkConnection() - 检查后端连接"
echo "debugAPI.testEndpoints() - 测试API接口"
echo ""
echo "按 Ctrl+C 停止服务器"
echo "==================================="

# 启动开发服务器
npm run dev
