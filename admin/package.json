{"name": "dingdong-admin", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "test-proxy": "node test-proxy.js", "start": "./start.sh"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "echarts": "^6.0.0", "element-plus": "^2.10.4", "vue": "^3.5.17", "vue-router": "^4.5.1"}, "devDependencies": {"@vitejs/plugin-vue": "^6.0.0", "vite": "^7.0.0", "vite-plugin-vue-devtools": "^7.7.7"}}