import { fileURLToPath, URL } from 'node:url'
import { defineConfig, loadEnv } from 'vite'
import vue from '@vitejs/plugin-vue'
import vueDevTools from 'vite-plugin-vue-devtools'

// https://vite.dev/config/
export default defineConfig(({ mode }) => {
  // 加载环境变量
  const env = loadEnv(mode, process.cwd(), '')
  const backendUrl = env.VITE_BACKEND_URL || 'http://s7838bb6.natappfree.cc'

  return {
    plugins: [
      vue(),
      vueDevTools(),
    ],
    resolve: {
      alias: {
        '@': fileURLToPath(new URL('./src', import.meta.url))
      },
    },
    server: {
      port: 5173,
      host: true,
      proxy: {
        // 代理所有 /api 请求到后端服务器
        '/api': {
          target: backendUrl,
          changeOrigin: true,
          secure: false,
          rewrite: (path) => {
            return path.replace(/^\/api/, '');
          },
          configure: (proxy, options) => {
            proxy.on('error', (err, req, res) => {
              console.log('代理错误:', err)
            })
            proxy.on('proxyReq', (proxyReq, req, res) => {
              console.log('代理请求:', req.method, req.url, '->', options.target + req.url)
            })
          },

          '/static': {
            target: backendUrl,
            changeOrigin: true,
            secure: false,
            rewrite: (path) => {
              return path;
            },
            configure: (proxy, options) => {
              proxy.on('error', (err, req, res) => {
                console.log('代理错误:', err)
              })
              proxy.on('proxyReq', (proxyReq, req, res) => {
                console.log('代理请求:', req.method, req.url, '->', options.target + req.url)
              })
            }
        }
      }
    }
  }
})
