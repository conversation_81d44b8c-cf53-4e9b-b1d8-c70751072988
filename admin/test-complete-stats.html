<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>完整统计功能测试 - 跑腿员统计 + 代理人统计</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-title {
            color: #333;
            border-bottom: 2px solid #667eea;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 24px;
            margin: 20px 0;
        }
        .chart-card {
            background: white;
            border-radius: 16px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
            border: 1px solid #e5e7eb;
            overflow: hidden;
        }
        .chart-header {
            padding: 20px 24px;
            border-bottom: 1px solid #e5e7eb;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .chart-header h3 {
            margin: 0;
            color: #374151;
            font-size: 1.1rem;
            font-weight: 600;
        }
        .chart-controls {
            display: flex;
            gap: 12px;
            align-items: center;
        }
        .chart-container {
            padding: 20px;
            height: 400px;
            display: flex;
            align-items: center;
            justify-content: center;
            background: #f9fafb;
            color: #6b7280;
            font-size: 1.1rem;
        }
        .ranking-container {
            padding: 20px;
            height: 400px;
            overflow-y: auto;
        }
        .ranking-table {
            width: 100%;
        }
        .table-header {
            display: grid;
            grid-template-columns: 60px 1fr 100px 100px 80px;
            gap: 16px;
            padding: 12px 16px;
            background: #f9fafb;
            border-radius: 8px;
            margin-bottom: 16px;
            font-weight: 600;
            color: #374151;
            font-size: 0.9rem;
        }
        .header-cell {
            display: flex;
            align-items: center;
            gap: 4px;
        }
        .sortable {
            cursor: pointer;
            user-select: none;
        }
        .sort-icon {
            font-size: 12px;
            color: #9ca3af;
        }
        .table-body {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }
        .table-row {
            display: grid;
            grid-template-columns: 60px 1fr 100px 100px 80px;
            gap: 16px;
            padding: 12px 16px;
            border-radius: 8px;
            transition: all 0.2s ease;
            cursor: pointer;
        }
        .table-row:hover {
            background: #f9fafb;
        }
        .table-row.top-3 {
            background: linear-gradient(135deg, #fef3c7, #fde68a);
            border: 1px solid #f59e0b;
        }
        .cell {
            display: flex;
            align-items: center;
            font-size: 0.9rem;
            color: #374151;
        }
        .rank-number {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            background: #e5e7eb;
            color: #6b7280;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            font-size: 0.8rem;
        }
        .rank-number.top-3 {
            background: linear-gradient(135deg, #fbbf24, #f59e0b);
            color: white;
        }
        .agent-info {
            display: flex;
            flex-direction: column;
            gap: 4px;
        }
        .agent-name {
            font-weight: 500;
            color: #1f2937;
        }
        .referrals, .orders, .rate {
            text-align: center;
            font-weight: 500;
        }
        .rate {
            color: #10b981;
        }
        .feature-list {
            list-style-type: none;
            padding: 0;
        }
        .feature-list li {
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }
        .feature-list li:before {
            content: "✓ ";
            color: #4caf50;
            font-weight: bold;
        }
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-weight: bold;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
        }
        .comparison-table th,
        .comparison-table td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        .comparison-table th {
            background: #f5f5f5;
            font-weight: bold;
        }
        .highlight {
            background: #fff3cd;
            font-weight: bold;
        }
        .mock-chart {
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.2rem;
            font-weight: 600;
        }
        .mock-pie {
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #10b981, #059669);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.2rem;
            font-weight: 600;
        }
        .mock-area {
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #f59e0b, #d97706);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.2rem;
            font-weight: 600;
        }
    </style>
</head>
<body>
    <h1>完整统计功能测试 - 跑腿员统计 + 代理人统计</h1>
    
    <div class="test-section">
        <h2 class="test-title">📊 新增统计模块</h2>
        
        <div class="status success">
            ✅ 已成功添加跑腿员统计和代理人统计两个完整模块
        </div>
        
        <h3>新增功能概览</h3>
        <ul class="feature-list">
            <li><strong>跑腿员统计模块</strong>：包含订单与重量趋势图和品类重量占比图</li>
            <li><strong>代理人统计模块</strong>：包含推荐趋势图和Top 10代理人排行榜</li>
            <li><strong>交互式图表</strong>：使用ECharts实现专业的数据可视化</li>
            <li><strong>时间范围选择</strong>：支持不同时间段的数据查看</li>
            <li><strong>响应式设计</strong>：适配各种屏幕尺寸</li>
        </ul>
    </div>

    <div class="test-section">
        <h2 class="test-title">🏃‍♂️ 跑腿员统计模块</h2>
        
        <div class="stats-grid">
            <!-- 订单与重量趋势 -->
            <div class="chart-card">
                <div class="chart-header">
                    <h3>订单与重量趋势</h3>
                </div>
                <div class="chart-container">
                    <div class="mock-chart">
                        订单与重量趋势图<br>
                        (ECharts 柱状图)
                    </div>
                </div>
            </div>

            <!-- 品类重量占比 -->
            <div class="chart-card">
                <div class="chart-header">
                    <h3>品类重量占比</h3>
                    <div class="chart-controls">
                        <select style="padding: 8px; border-radius: 4px; border: 1px solid #ddd;">
                            <option>时间范围: 本月</option>
                        </select>
                        <button style="padding: 8px 16px; background: #667eea; color: white; border: none; border-radius: 4px; cursor: pointer;">
                            导出报表
                        </button>
                    </div>
                </div>
                <div class="chart-container">
                    <div class="mock-pie">
                        品类重量占比图<br>
                        (ECharts 环形图)
                    </div>
                </div>
            </div>
        </div>

        <h3>跑腿员统计功能特点</h3>
        <ul class="feature-list">
            <li><strong>订单趋势分析</strong>：显示每日订单数量变化趋势</li>
            <li><strong>重量占比分析</strong>：展示不同品类物品的重量分布</li>
            <li><strong>时间范围选择</strong>：支持本周、本月、本季度、本年数据查看</li>
            <li><strong>数据导出</strong>：支持报表导出功能</li>
            <li><strong>响应式布局</strong>：在不同设备上都有良好的显示效果</li>
        </ul>
    </div>

    <div class="test-section">
        <h2 class="test-title">👨‍💼 代理人统计模块</h2>
        
        <div class="stats-grid">
            <!-- 推荐趋势 -->
            <div class="chart-card">
                <div class="chart-header">
                    <h3>推荐趋势</h3>
                </div>
                <div class="chart-container">
                    <div class="mock-area">
                        推荐趋势图<br>
                        (ECharts 面积图)
                    </div>
                </div>
            </div>

            <!-- Top 10 代理人 -->
            <div class="chart-card">
                <div class="chart-header">
                    <h3>Top 10 代理人</h3>
                    <div class="chart-controls">
                        <select style="padding: 8px; border-radius: 4px; border: 1px solid #ddd;">
                            <option>时间范围: 本月</option>
                        </select>
                    </div>
                </div>
                <div class="ranking-container">
                    <div class="ranking-table">
                        <div class="table-header">
                            <div class="header-cell rank">排名</div>
                            <div class="header-cell agent">代理人</div>
                            <div class="header-cell referrals sortable">
                                推荐人数
                                <span class="sort-icon">↓</span>
                            </div>
                            <div class="header-cell orders">推荐订单</div>
                            <div class="header-cell rate">转化率</div>
                        </div>
                        <div class="table-body">
                            <div class="table-row top-3">
                                <div class="cell rank">
                                    <span class="rank-number top-3">1</span>
                                </div>
                                <div class="cell agent">
                                    <div class="agent-info">
                                        <div class="agent-name">张晓明</div>
                                    </div>
                                </div>
                                <div class="cell referrals">128</div>
                                <div class="cell orders">356</div>
                                <div class="cell rate">2.78%</div>
                            </div>
                            <div class="table-row top-3">
                                <div class="cell rank">
                                    <span class="rank-number top-3">2</span>
                                </div>
                                <div class="cell agent">
                                    <div class="agent-info">
                                        <div class="agent-name">李志强</div>
                                    </div>
                                </div>
                                <div class="cell referrals">115</div>
                                <div class="cell orders">320</div>
                                <div class="cell rate">2.78%</div>
                            </div>
                            <div class="table-row top-3">
                                <div class="cell rank">
                                    <span class="rank-number top-3">3</span>
                                </div>
                                <div class="cell agent">
                                    <div class="agent-info">
                                        <div class="agent-name">王芳</div>
                                    </div>
                                </div>
                                <div class="cell referrals">98</div>
                                <div class="cell orders">280</div>
                                <div class="cell rate">2.86%</div>
                            </div>
                            <div class="table-row">
                                <div class="cell rank">
                                    <span class="rank-number">4</span>
                                </div>
                                <div class="cell agent">
                                    <div class="agent-info">
                                        <div class="agent-name">赵伟</div>
                                    </div>
                                </div>
                                <div class="cell referrals">86</div>
                                <div class="cell orders">245</div>
                                <div class="cell rate">2.85%</div>
                            </div>
                            <div class="table-row">
                                <div class="cell rank">
                                    <span class="rank-number">5</span>
                                </div>
                                <div class="cell agent">
                                    <div class="agent-info">
                                        <div class="agent-name">陈静</div>
                                    </div>
                                </div>
                                <div class="cell referrals">78</div>
                                <div class="cell orders">210</div>
                                <div class="cell rate">2.69%</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <h3>代理人统计功能特点</h3>
        <ul class="feature-list">
            <li><strong>推荐趋势分析</strong>：展示推荐人数和推荐订单数的变化趋势</li>
            <li><strong>排行榜系统</strong>：显示Top 10代理人的详细数据</li>
            <li><strong>数据排序</strong>：支持按推荐人数等指标排序</li>
            <li><strong>前三名突出</strong>：前三名有特殊的视觉标识</li>
            <li><strong>转化率分析</strong>：显示每个代理人的转化率数据</li>
        </ul>
    </div>

    <div class="test-section">
        <h2 class="test-title">📋 功能对比表</h2>
        
        <table class="comparison-table">
            <thead>
                <tr>
                    <th>功能模块</th>
                    <th>原有功能</th>
                    <th>新增功能</th>
                    <th>状态</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>统计指标</td>
                    <td>6个基础统计卡片</td>
                    <td>+2个专业统计模块</td>
                    <td>✅ 已完成</td>
                </tr>
                <tr>
                    <td>图表类型</td>
                    <td>折线图、饼图</td>
                    <td>+柱状图、面积图、排行榜</td>
                    <td>✅ 已完成</td>
                </tr>
                <tr>
                    <td>数据维度</td>
                    <td>基础业务数据</td>
                    <td>+人员绩效数据</td>
                    <td>✅ 已完成</td>
                </tr>
                <tr>
                    <td>交互功能</td>
                    <td>基础图表</td>
                    <td>+时间选择、排序、导出</td>
                    <td>✅ 已完成</td>
                </tr>
                <tr>
                    <td>响应式设计</td>
                    <td>基础响应式</td>
                    <td>+专业统计模块响应式</td>
                    <td>✅ 已完成</td>
                </tr>
            </tbody>
        </table>
    </div>

    <div class="test-section">
        <h2 class="test-title">🔧 技术实现细节</h2>
        
        <h3>组件架构</h3>
        <ul class="feature-list">
            <li><strong>RunnerStats.vue</strong>：跑腿员统计组件，包含趋势图和占比图</li>
            <li><strong>AgentStats.vue</strong>：代理人统计组件，包含趋势图和排行榜</li>
            <li><strong>Dashboard.vue</strong>：主页面，集成所有统计组件</li>
        </ul>

        <h3>图表技术</h3>
        <ul class="feature-list">
            <li><strong>ECharts</strong>：专业的数据可视化库</li>
            <li><strong>响应式图表</strong>：自动适应容器大小变化</li>
            <li><strong>主题定制</strong>：使用系统配色方案</li>
            <li><strong>交互功能</strong>：支持悬停、点击等交互</li>
        </ul>

        <h3>数据管理</h3>
        <ul class="feature-list">
            <li><strong>响应式数据</strong>：使用Vue 3 Composition API</li>
            <li><strong>时间范围控制</strong>：支持多种时间维度</li>
            <li><strong>数据监听</strong>：实时响应数据变化</li>
            <li><strong>API集成</strong>：预留API接口调用位置</li>
        </ul>
    </div>

    <div class="test-section">
        <h2 class="test-title">📱 响应式设计特性</h2>
        
        <h3>屏幕适配</h3>
        <ul class="feature-list">
            <li><strong>大屏幕 (>1200px)</strong>：2列布局，图表高度400px</li>
            <li><strong>中等屏幕 (768px-1200px)</strong>：1列布局，图表高度350px</li>
            <li><strong>小屏幕 (<768px)</strong>：1列布局，图表高度300px</li>
            <li><strong>超小屏幕 (<480px)</strong>：优化间距和字体大小</li>
        </ul>

        <h3>布局自适应</h3>
        <ul class="feature-list">
            <li><strong>Grid布局</strong>：自动调整列数和间距</li>
            <li><strong>弹性容器</strong>：图表容器自适应大小</li>
            <li><strong>触摸友好</li>：移动端优化的交互体验</li>
            <li><strong>性能优化</strong>：图表重绘和事件处理优化</li>
        </ul>
    </div>

    <div class="test-section">
        <h2 class="test-title">✅ 验证步骤</h2>
        
        <div class="status info">
            ℹ️ 请访问主页面验证完整的统计功能
        </div>
        
        <ol>
            <li>登录系统后进入主页面</li>
            <li>确认显示6个统计卡片（包含代理人和跑腿员统计）</li>
            <li>向下滚动查看跑腿员统计模块</li>
            <li>验证订单与重量趋势图和品类重量占比图</li>
            <li>查看代理人统计模块</li>
            <li>验证推荐趋势图和Top 10代理人排行榜</li>
            <li>测试时间范围选择功能</li>
            <li>测试不同屏幕尺寸下的显示效果</li>
        </ol>
        
        <h3>预期效果</h3>
        <ul class="feature-list">
            <li>✅ 6个统计卡片完整显示</li>
            <li>✅ 跑腿员统计模块正常显示</li>
            <li>✅ 代理人统计模块正常显示</li>
            <li>✅ 所有图表正常渲染</li>
            <li>✅ 排行榜数据完整显示</li>
            <li>✅ 响应式布局正常工作</li>
            <li>✅ 交互功能正常响应</li>
        </ul>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            console.log('完整统计功能测试页面已加载');
            console.log('包含跑腿员统计和代理人统计两个模块');
            
            // 模拟图表点击
            const mockCharts = document.querySelectorAll('.mock-chart, .mock-pie, .mock-area');
            mockCharts.forEach((chart, index) => {
                chart.addEventListener('click', function() {
                    const chartType = this.classList.contains('mock-chart') ? '柱状图' : 
                                    this.classList.contains('mock-pie') ? '环形图' : '面积图';
                    alert(`在实际页面中，这里会显示${chartType}的详细数据\n点击图表可以查看具体数值和趋势`);
                });
            });
            
            // 模拟排行榜行点击
            const tableRows = document.querySelectorAll('.table-row');
            tableRows.forEach((row, index) => {
                row.addEventListener('click', function() {
                    const rank = index + 1;
                    const name = this.querySelector('.agent-name').textContent;
                    alert(`点击了第${rank}名：${name}\n在实际页面中会显示该代理人的详细数据`);
                });
            });
        });
    </script>
</body>
</html>
