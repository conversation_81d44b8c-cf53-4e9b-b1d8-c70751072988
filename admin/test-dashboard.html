<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能回收管理系统 - Dashboard测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f5f7fa;
        }
        .dashboard-container {
            padding: 24px;
            background: #f5f7fa;
            min-height: 100vh;
        }
        .page-header {
            margin-bottom: 32px;
            text-align: center;
        }
        .page-title {
            font-size: 2.5rem;
            font-weight: 700;
            color: #1f2937;
            margin-bottom: 8px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        .page-subtitle {
            font-size: 1.1rem;
            color: #6b7280;
            margin: 0;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 24px;
            margin-bottom: 32px;
        }
        .stat-card {
            background: white;
            padding: 24px;
            border-radius: 16px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
            border: 1px solid #e5e7eb;
            display: flex;
            align-items: center;
            gap: 16px;
            transition: all 0.3s ease;
        }
        .stat-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 12px 24px rgba(0, 0, 0, 0.1);
        }
        .stat-icon {
            width: 60px;
            height: 60px;
            border-radius: 16px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
        }
        .stat-content {
            flex: 1;
        }
        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            color: #1f2937;
            margin-bottom: 4px;
        }
        .stat-label {
            font-size: 0.9rem;
            color: #6b7280;
        }
        .stat-change {
            font-size: 0.9rem;
            font-weight: 600;
            padding: 4px 8px;
            border-radius: 8px;
        }
        .stat-change.positive {
            color: #10b981;
            background: rgba(16, 185, 129, 0.1);
        }
        .stat-change.negative {
            color: #ef4444;
            background: rgba(239, 68, 68, 0.1);
        }
        .charts-section {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 24px;
            margin-bottom: 32px;
        }
        .chart-container {
            background: white;
            border-radius: 16px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
            border: 1px solid #e5e7eb;
            overflow: hidden;
        }
        .chart-header {
            padding: 20px 24px;
            border-bottom: 1px solid #e5e7eb;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .chart-header h3 {
            margin: 0;
            font-size: 1.2rem;
            font-weight: 600;
            color: #1f2937;
        }
        .chart-content {
            height: 300px;
            padding: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #6b7280;
            font-size: 0.9rem;
        }
        .modules-section {
            margin-bottom: 32px;
        }
        .section-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 20px;
        }
        .modules-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }
        .module-card {
            background: white;
            padding: 24px;
            border-radius: 16px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
            border: 1px solid #e5e7eb;
            display: flex;
            align-items: center;
            gap: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
        }
        .module-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 12px 24px rgba(0, 0, 0, 0.1);
            border-color: #667eea;
        }
        .module-icon {
            width: 56px;
            height: 56px;
            border-radius: 16px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 20px;
        }
        .module-content {
            flex: 1;
        }
        .module-content h3 {
            margin: 0 0 8px 0;
            font-size: 1.1rem;
            font-weight: 600;
            color: #1f2937;
        }
        .module-content p {
            margin: 0;
            font-size: 0.9rem;
            color: #6b7280;
            line-height: 1.4;
        }
        .arrow-icon {
            color: #9ca3af;
            font-size: 16px;
            transition: transform 0.3s ease;
        }
        .module-card:hover .arrow-icon {
            transform: translateX(4px);
            color: #667eea;
        }
        .recent-activity {
            margin-bottom: 32px;
        }
        .activity-list {
            background: white;
            border-radius: 16px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
            border: 1px solid #e5e7eb;
            overflow: hidden;
        }
        .activity-item {
            display: flex;
            align-items: center;
            gap: 16px;
            padding: 20px 24px;
            border-bottom: 1px solid #f3f4f6;
            transition: background-color 0.3s ease;
        }
        .activity-item:last-child {
            border-bottom: none;
        }
        .activity-item:hover {
            background-color: #f9fafb;
        }
        .activity-icon {
            width: 40px;
            height: 40px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 16px;
        }
        .activity-icon.order {
            background: linear-gradient(135deg, #3b82f6, #1d4ed8);
        }
        .activity-icon.payment {
            background: linear-gradient(135deg, #10b981, #059669);
        }
        .activity-icon.user {
            background: linear-gradient(135deg, #f59e0b, #d97706);
        }
        .activity-icon.system {
            background: linear-gradient(135deg, #8b5cf6, #7c3aed);
        }
        .activity-content {
            flex: 1;
        }
        .activity-title {
            font-size: 0.95rem;
            font-weight: 600;
            color: #1f2937;
            margin-bottom: 4px;
        }
        .activity-desc {
            font-size: 0.85rem;
            color: #6b7280;
            margin-bottom: 4px;
        }
        .activity-time {
            font-size: 0.75rem;
            color: #9ca3af;
        }
        .icon-placeholder {
            font-size: 20px;
        }
        @media (max-width: 1024px) {
            .charts-section {
                grid-template-columns: 1fr;
            }
            .chart-content {
                height: 250px;
            }
        }
        @media (max-width: 768px) {
            .dashboard-container {
                padding: 16px;
            }
            .page-title {
                font-size: 2rem;
            }
            .stats-grid {
                grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
                gap: 16px;
            }
            .stat-card {
                padding: 20px;
            }
            .stat-icon {
                width: 50px;
                height: 50px;
                font-size: 20px;
            }
            .stat-number {
                font-size: 1.5rem;
            }
            .modules-grid {
                grid-template-columns: 1fr;
            }
            .chart-content {
                height: 200px;
            }
        }
        @media (max-width: 480px) {
            .page-title {
                font-size: 1.8rem;
            }
            .stats-grid {
                grid-template-columns: 1fr;
            }
            .stat-card {
                flex-direction: column;
                text-align: center;
                gap: 12px;
            }
            .chart-header {
                flex-direction: column;
                gap: 12px;
                align-items: flex-start;
            }
        }
    </style>
</head>
<body>
    <div class="dashboard-container">
        <!-- 页面标题 -->
        <div class="page-header">
            <h1 class="page-title">智能回收管理系统</h1>
            <p class="page-subtitle">欢迎回来，管理员</p>
        </div>

        <!-- 数据统计卡片 -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-icon">
                    <span class="icon-placeholder">📊</span>
                </div>
                <div class="stat-content">
                    <div class="stat-number">1,234</div>
                    <div class="stat-label">今日回收量</div>
                </div>
                <div class="stat-change positive">+12.5%</div>
            </div>

            <div class="stat-card">
                <div class="stat-icon">
                    <span class="icon-placeholder">💰</span>
                </div>
                <div class="stat-content">
                    <div class="stat-number">¥8,567</div>
                    <div class="stat-label">今日收入</div>
                </div>
                <div class="stat-change positive">+8.3%</div>
            </div>

            <div class="stat-card">
                <div class="stat-icon">
                    <span class="icon-placeholder">👥</span>
                </div>
                <div class="stat-content">
                    <div class="stat-number">89</div>
                    <div class="stat-label">活跃用户</div>
                </div>
                <div class="stat-change positive">+5.2%</div>
            </div>

            <div class="stat-card">
                <div class="stat-icon">
                    <span class="icon-placeholder">📦</span>
                </div>
                <div class="stat-content">
                    <div class="stat-number">456</div>
                    <div class="stat-label">待处理订单</div>
                </div>
                <div class="stat-change negative">-2.1%</div>
            </div>

            <div class="stat-card">
                <div class="stat-icon">
                    <span class="icon-placeholder">👨‍💼</span>
                </div>
                <div class="stat-content">
                    <div class="stat-number">156</div>
                    <div class="stat-label">代理人总数</div>
                </div>
                <div class="stat-change positive">+3.8%</div>
            </div>

            <div class="stat-card">
                <div class="stat-icon">
                    <span class="icon-placeholder">🚚</span>
                </div>
                <div class="stat-content">
                    <div class="stat-number">89</div>
                    <div class="stat-label">跑腿员总数</div>
                </div>
                <div class="stat-change positive">+6.7%</div>
            </div>
        </div>

        <!-- 图表区域 -->
        <div class="charts-section">
            <div class="chart-container">
                <div class="chart-header">
                    <h3>回收量趋势</h3>
                    <div class="chart-actions">
                        <select style="padding: 4px 8px; border-radius: 4px; border: 1px solid #d1d5db;">
                            <option>最近7天</option>
                            <option>最近30天</option>
                            <option>最近90天</option>
                        </select>
                    </div>
                </div>
                <div class="chart-content">
                    图表区域 - 回收量趋势图
                    <br>
                    (在实际页面中会显示ECharts图表)
                </div>
            </div>

            <div class="chart-container">
                <div class="chart-header">
                    <h3>回收类型分布</h3>
                </div>
                <div class="chart-content">
                    图表区域 - 回收类型分布图
                    <br>
                    (在实际页面中会显示ECharts饼图)
                </div>
            </div>
        </div>

        <!-- 功能模块 -->
        <div class="modules-section">
            <h2 class="section-title">功能模块</h2>
            <div class="modules-grid">
                <div class="module-card">
                    <div class="module-icon">
                        <span class="icon-placeholder">📄</span>
                    </div>
                    <div class="module-content">
                        <h3>订单管理</h3>
                        <p>管理回收订单，跟踪处理状态</p>
                    </div>
                    <span class="arrow-icon">→</span>
                </div>

                <div class="module-card">
                    <div class="module-icon">
                        <span class="icon-placeholder">👤</span>
                    </div>
                    <div class="module-content">
                        <h3>用户管理</h3>
                        <p>管理系统用户和权限设置</p>
                    </div>
                    <span class="arrow-icon">→</span>
                </div>

                <div class="module-card">
                    <div class="module-icon">
                        <span class="icon-placeholder">📦</span>
                    </div>
                    <div class="module-content">
                        <h3>回收管理</h3>
                        <p>管理回收物品和价格设置</p>
                    </div>
                    <span class="arrow-icon">→</span>
                </div>

                <div class="module-card">
                    <div class="module-icon">
                        <span class="icon-placeholder">📊</span>
                    </div>
                    <div class="module-content">
                        <h3>数据报表</h3>
                        <p>查看详细的数据统计和分析</p>
                    </div>
                    <span class="arrow-icon">→</span>
                </div>
            </div>
        </div>

        <!-- 最近活动 -->
        <div class="recent-activity">
            <h2 class="section-title">最近活动</h2>
            <div class="activity-list">
                <div class="activity-item">
                    <div class="activity-icon order">
                        <span class="icon-placeholder">📄</span>
                    </div>
                    <div class="activity-content">
                        <div class="activity-title">新订单创建</div>
                        <div class="activity-desc">用户张三创建了新的回收订单</div>
                        <div class="activity-time">2分钟前</div>
                    </div>
                </div>

                <div class="activity-item">
                    <div class="activity-icon payment">
                        <span class="icon-placeholder">💰</span>
                    </div>
                    <div class="activity-content">
                        <div class="activity-title">支付完成</div>
                        <div class="activity-desc">订单#12345支付完成，金额¥156.00</div>
                        <div class="activity-time">15分钟前</div>
                    </div>
                </div>

                <div class="activity-item">
                    <div class="activity-icon user">
                        <span class="icon-placeholder">👤</span>
                    </div>
                    <div class="activity-content">
                        <div class="activity-title">新用户注册</div>
                        <div class="activity-desc">新用户李四完成注册</div>
                        <div class="activity-time">1小时前</div>
                    </div>
                </div>

                <div class="activity-item">
                    <div class="activity-icon system">
                        <span class="icon-placeholder">⚙️</span>
                    </div>
                    <div class="activity-content">
                        <div class="activity-title">系统更新</div>
                        <div class="activity-desc">系统完成自动更新维护</div>
                        <div class="activity-time">2小时前</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            console.log('智能回收管理系统Dashboard测试页面已加载');
            console.log('请访问主页面查看实际的Dashboard效果');
            
            // 模拟交互效果
            const moduleCards = document.querySelectorAll('.module-card');
            moduleCards.forEach(card => {
                card.addEventListener('click', function() {
                    const title = this.querySelector('h3').textContent;
                    alert(`点击了${title}模块 - 在实际页面中会跳转到对应页面`);
                });
            });
            
            const statCards = document.querySelectorAll('.stat-card');
            statCards.forEach(card => {
                card.addEventListener('click', function() {
                    console.log('统计卡片点击 - 在实际页面中会显示详细数据');
                });
            });
        });
    </script>
</body>
</html>
