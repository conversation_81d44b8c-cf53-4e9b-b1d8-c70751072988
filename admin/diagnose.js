#!/usr/bin/env node

/**
 * 验证码问题诊断脚本
 */

import http from 'http'
import https from 'https'
import net from 'net'

console.log('🔍 开始诊断验证码问题...')
console.log('=' .repeat(50))

// 测试函数
async function testEndpoint(url, description) {
  return new Promise((resolve) => {
    console.log(`\n📡 测试 ${description}`)
    console.log(`URL: ${url}`)
    
    const client = url.startsWith('https') ? https : http
    const req = client.get(url, (res) => {
      let data = ''
      res.on('data', chunk => data += chunk)
      res.on('end', () => {
        try {
          const result = JSON.parse(data)
          console.log(`状态码: ${res.statusCode}`)
          console.log(`成功标识: ${result.success}`)
          
          if (result.data) {
            console.log(`验证码ID: ${result.data.captchaId}`)
            console.log(`图片数据长度: ${result.data.imageBase64?.length || 0}`)
            console.log(`提示文字: ${result.data.promptText}`)
            console.log(`图片尺寸: ${result.data.width}x${result.data.height}`)
          }
          
          resolve({ success: true, data: result })
        } catch (e) {
          console.log(`JSON解析失败: ${e.message}`)
          console.log(`原始响应: ${data.substring(0, 200)}...`)
          resolve({ success: false, error: e.message })
        }
      })
    })
    
    req.on('error', (err) => {
      console.log(`请求失败: ${err.message}`)
      resolve({ success: false, error: err.message })
    })
    
    req.setTimeout(10000, () => {
      console.log(`请求超时`)
      req.destroy()
      resolve({ success: false, error: 'timeout' })
    })
  })
}

// 检查端口是否开放
async function checkPort(host, port, description) {
  return new Promise((resolve) => {
    console.log(`\n🔌 检查 ${description} (${host}:${port})`)
    
    const socket = new net.Socket()
    socket.setTimeout(3000)
    
    socket.on('connect', () => {
      console.log(`端口 ${port} 开放`)
      socket.destroy()
      resolve(true)
    })
    
    socket.on('timeout', () => {
      console.log(`端口 ${port} 连接超时`)
      socket.destroy()
      resolve(false)
    })
    
    socket.on('error', (err) => {
      console.log(`端口 ${port} 连接失败: ${err.message}`)
      resolve(false)
    })
    
    socket.connect(port, host)
  })
}

// 主诊断函数
async function runDiagnosis() {
  console.log('🏥 系统诊断报告')
  console.log('时间:', new Date().toISOString())
  
  // 1. 检查端口
  const backendPortOpen = await checkPort('localhost', 8081, '后端服务')
  const frontendPortOpen = await checkPort('localhost', 5173, '前端服务')
  
  // 2. 测试API端点
  const backendTest = await testEndpoint('http://localhost:8081/api/captcha/generate', '后端直连')
  const proxyTest = await testEndpoint('http://localhost:5173/api/captcha/generate', '前端代理')
  
  // 3. 生成诊断报告
  console.log('\n' + '='.repeat(50))
  console.log('📋 诊断结果汇总')
  console.log('='.repeat(50))
  
  console.log(`\n🔌 端口检查:`)
  console.log(`  后端服务 (8081): ${backendPortOpen ? '开放' : '关闭'}`)
  console.log(`  前端服务 (5173): ${frontendPortOpen ? '开放' : '关闭'}`)
  
  console.log(`\n📡 API测试:`)
  console.log(`  后端直连: ${backendTest.success ? '成功' : '失败'}`)
  console.log(`  前端代理: ${proxyTest.success ? '成功' : '失败'}`)
  
  // 4. 问题分析和建议
  console.log(`\n🔧 问题分析:`)
  
  if (!backendPortOpen) {
    console.log(`后端服务未启动`)
    console.log(`   解决方案: cd .. && mvn spring-boot:run`)
  }
  
  if (!frontendPortOpen) {
    console.log(`前端服务未启动`)
    console.log(`   解决方案: npm run dev`)
  }
  
  if (backendPortOpen && !backendTest.success) {
    console.log(`后端API异常`)
    console.log(`   检查后端日志和配置`)
  }
  
  if (frontendPortOpen && backendTest.success && !proxyTest.success) {
    console.log(`前端代理配置问题`)
    console.log(`   检查 vite.config.js 代理配置`)
  }
  
  if (backendTest.success && proxyTest.success) {
    console.log(`API层面正常，问题可能在前端组件`)
    console.log(`   建议:`)
    console.log(`   1. 检查浏览器控制台错误`)
    console.log(`   2. 检查Vue组件状态管理`)
    console.log(`   3. 使用简化测试组件排查`)
  }
  
  console.log(`\n🌐 测试地址:`)
  console.log(`  前端: http://localhost:5173`)
  console.log(`  后端: http://localhost:8081`)
  console.log(`  API文档: http://localhost:8081/swagger-ui.html`)
  console.log(`  测试页面: http://localhost:5173/test-captcha.html`)
  
  console.log('\n' + '='.repeat(50))
  console.log('诊断完成')
}

// 运行诊断
runDiagnosis().catch(console.error)
