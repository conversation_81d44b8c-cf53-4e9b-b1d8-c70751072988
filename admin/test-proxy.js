#!/usr/bin/env node

/**
 * 测试代理配置脚本
 */

const http = require('http')

console.log('🧪 测试代理配置...')
console.log('=================================')

// 测试后端服务
function testBackend() {
  return new Promise((resolve) => {
    const req = http.get('http://localhost:8081/api/captcha/generate', (res) => {
      console.log('后端服务状态:', res.statusCode)
      resolve(res.statusCode === 200)
    })
    
    req.on('error', (err) => {
      console.log('后端服务连接失败:', err.message)
      resolve(false)
    })
    
    req.setTimeout(5000, () => {
      console.log('后端服务连接超时')
      req.destroy()
      resolve(false)
    })
  })
}

// 测试前端开发服务器
function testFrontend() {
  return new Promise((resolve) => {
    const req = http.get('http://localhost:5173', (res) => {
      console.log('前端服务状态:', res.statusCode)
      resolve(res.statusCode === 200)
    })
    
    req.on('error', (err) => {
      console.log('前端服务连接失败:', err.message)
      resolve(false)
    })
    
    req.setTimeout(5000, () => {
      console.log('前端服务连接超时')
      req.destroy()
      resolve(false)
    })
  })
}

// 主测试函数
async function runTests() {
  console.log('1. 测试后端服务 (http://localhost:8081)...')
  const backendOk = await testBackend()
  
  console.log('\n2. 测试前端服务 (http://localhost:5173)...')
  const frontendOk = await testFrontend()
  
  console.log('\n=================================')
  console.log('测试结果:')
  console.log('后端服务:', backendOk ? '正常' : '异常')
  console.log('前端服务:', frontendOk ? '正常' : '异常')
  
  if (backendOk && frontendOk) {
    console.log('\n🎉 所有服务正常，可以开始使用！')
    console.log('访问地址: http://localhost:5173')
  } else {
    console.log('\n⚠️  请检查服务状态:')
    if (!backendOk) {
      console.log('- 启动后端: cd .. && mvn spring-boot:run')
    }
    if (!frontendOk) {
      console.log('- 启动前端: npm run dev')
    }
  }
  
  console.log('=================================')
}

// 运行测试
runTests().catch(console.error)
